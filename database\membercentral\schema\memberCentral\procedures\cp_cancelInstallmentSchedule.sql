ALTER PROC dbo.cp_cancelInstallmentSchedule
@siteID int,
@scheduleID int,
@AROption char(1),
@performedByMemberID int,
@statsSessionID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @orgID int, @memberID int, @minTID int, @contributionID int, @dueDate date, @adjAmount decimal(18,2), 
		@GLAccountID int, @detail varchar(500), @transactionID int, @adjustCount int, @offsetCount int,
		@nowDateTime datetime, @nowDate date, @invoiceProfileID int, @invoiceID int, @invoiceNumber varchar(18), 
		@programID int, @trashID int, @tr_InstallOffsetTrans int, @tr_InstallSaleTrans int;
	DECLARE @tblTIDs TABLE (transactionID int PRIMARY KEY, typeID int, creditGLAccountID int, statusID int, amount decimal(18,2), detail varchar(500), scheduleID int);
	DECLARE @tblInvoices TABLE (invoiceID int PRIMARY KEY, invoiceProfileID int);
	DECLARE @tblAdjust TABLE (transactionID int PRIMARY KEY, amountToAdjust decimal(18,2), creditGLAccountID int);
	DECLARE @tblOffsets TABLE (transactionID int PRIMARY KEY, amount decimal(18,2), dueDate date, GLAccountID int, scheduleID int, detail varchar(500));
	
	select @orgID = orgID from dbo.sites where siteID = @siteID;
	set @nowDateTime = GETDATE();
	set @nowDate = @nowDateTime;
	set @tr_InstallSaleTrans = dbo.fn_tr_getRelationshipTypeID('InstallSaleTrans');
	set @tr_InstallOffsetTrans = dbo.fn_tr_getRelationshipTypeID('InstallOffsetTrans');

	select top 1 @contributionID = c.contributionID, @programID = c.programID, @memberID = c.memberID 
	from dbo.cp_contributionSchedule as cs
	inner join dbo.cp_contributions as c on cs.contributionID = c.contributionID
	where cs.scheduleID = @scheduleID;

	-- get all transactions tied to this contribution
	INSERT INTO @tblTIDs (transactionID, typeID, creditGLAccountID, statusID, amount, detail, scheduleID)
	select transactionID, typeID, creditGLAccountID, statusID, amount, detail, scheduleID
	from dbo.fn_cp_contributionTransactions(@contributionID);

	-- put all open invoices used for contributor into table since they were already created and can be used for adjustments
	insert into @tblInvoices (invoiceID, invoiceProfileID)
	select distinct i.invoiceID, i.invoiceProfileID
	from @tblTIDs as ct
	inner join dbo.tr_invoiceTransactions as it on it.orgID = @orgID and it.transactionID = ct.transactionID
	inner join dbo.tr_invoices as i on i.orgID = @orgID and i.invoiceID = it.invoiceID and i.statusID = 1;

	IF @AROption = 'A'
		-- get all contribution-related sales transactions for this schedule we need to adjust
		INSERT INTO @tblAdjust (transactionID, amountToAdjust, creditGLAccountID)
		select ct.transactionID, tsFull.cache_amountAfterAdjustment, ct.creditGLAccountID
		from @tblTIDs as ct
		cross apply dbo.fn_tr_transactionSalesWithDIT(@orgID,ct.transactionID) as tsFull
		inner join dbo.tr_transactionInstallments as ti on ti.orgID = @orgID and ti.scheduleID = ct.scheduleID
			and ti.isActive = 1
		where ct.typeID = 1
		and ct.statusID = 1
		and ct.scheduleID = @scheduleID
		and tsFull.cache_amountAfterAdjustment > 0
		OPTION(RECOMPILE);

	ELSE
		-- get all unpaid sales transactions for this schedule we need to adjust
		INSERT INTO @tblAdjust (transactionID, amountToAdjust, creditGLAccountID)
		select ct.transactionID, tsFull.cache_amountAfterAdjustment - tsFull.cache_activePaymentAllocatedAmount - tsFull.cache_pendingPaymentAllocatedAmount, ct.creditGLAccountID
		from @tblTIDs as ct
		cross apply dbo.fn_tr_transactionSalesWithDIT(@orgID,ct.transactionID) as tsFull
		inner join dbo.tr_transactionInstallments as ti on ti.orgID = @orgID and ti.scheduleID = ct.scheduleID
			and ti.isActive = 1
		where ct.typeID = 1
		and ct.statusID = 1
		and ct.scheduleID = @scheduleID
		and tsFull.cache_amountAfterAdjustment - tsFull.cache_activePaymentAllocatedAmount - tsFull.cache_pendingPaymentAllocatedAmount > 0
		OPTION(RECOMPILE);

	set @adjustCount = @@ROWCOUNT;

	-- get all installments for this schedule we need to offset
	INSERT INTO @tblOffsets (transactionID, amount, dueDate, GLAccountID, scheduleID, detail)
	select ct.transactionID, ct.amount, ti.dueDate, ct.creditGLAccountID, ti.scheduleID, ct.detail
	from @tblTIDs as ct
	inner join dbo.tr_transactionInstallments as ti on ti.orgID = @orgID and ti.transactionID = ct.transactionID
	where ct.statusID = 1
	and ti.scheduleID = @scheduleID
	and ti.isActive = 1
	and ti.isConverted = 0
	and ti.isPaidOnCreate = 0;
		set @offsetCount = @@ROWCOUNT;

	BEGIN TRAN;
		-- all active sale transactions connected to this contribution schedule should be adjusted to 0.
		IF @adjustCount > 0 BEGIN
			SELECT @minTID = min(transactionID) from @tblAdjust;
			WHILE @minTID IS NOT NULL BEGIN
				select @invoiceProfileID = null, @invoiceID = null, @adjAmount = null, @GLAccountID = null;

				select @adjAmount = amountToAdjust*-1, @GLAccountID = creditGLAccountID from @tblAdjust where transactionID = @minTID;
				select @invoiceProfileID = invoiceProfileID from dbo.tr_glAccounts where orgID = @orgID and glAccountID = @GLAccountID;
				select top 1 @invoiceID = invoiceID from @tblInvoices where invoiceProfileID = @invoiceProfileID;
				
				-- if necessary, create invoice assigned to contributor based on invoice profile
				IF @invoiceID is null BEGIN
					EXEC dbo.tr_createInvoice @invoiceProfileID=@invoiceProfileID, @enteredByMemberID=@performedByMemberID, 
						@assignedToMemberID=@memberID, @dateBilled=@nowDate, @dateDue=@nowdate, 
						@invoiceID=@invoiceID OUTPUT, @invoiceNumber=@invoiceNumber OUTPUT;

					insert into @tblInvoices (invoiceID, invoiceProfileID)
					values (@invoiceID, @invoiceProfileID);
				END	

				EXEC dbo.tr_createTransaction_adjustment @recordedOnSiteID=@siteID, @recordedByMemberID=@performedByMemberID, 
					@statsSessionID=null, @amount=@adjAmount, @taxAmount=null, @transactionDate=@nowDateTime,
					@autoAdjustTransactionDate=1, @saleTransactionID=@minTID, @invoiceID=@invoiceID, @byPassTax=0, 
					@byPassAccrual=0, @xmlSchedule=null, @transactionID=@trashID OUTPUT;
				
				SELECT @minTID = min(transactionID) from @tblAdjust where transactionID > @minTID;
			END
		END

		-- all active installment transactions tied to this schedule not yet converted to sales should have an installment offset
		IF @offsetCount > 0 BEGIN
			set @minTID = null;
			select @minTID = min(transactionID) from @tblOffsets;
			WHILE @minTID is not null BEGIN
				select @dueDate = null, @adjAmount = null, @GLAccountID = null, @detail = null, @transactionID = null;

				select @dueDate=dueDate, @GLAccountID=GLAccountID, @adjAmount=amount*-1, @detail=detail
				from @tblOffsets
				where transactionID = @minTID;

				EXEC dbo.tr_createTransaction_installment @ownedByOrgID=@orgID, @recordedOnSiteID=@siteID, 
					@assignedToMemberID=@memberID, @recordedByMemberID=@performedByMemberID, @statsSessionID=null, 
					@detail=@detail, @amount=@adjAmount, @transactionDate=@dueDate, @revenueGLAccountID=@GLAccountID, 
					@scheduleID=@scheduleID, @offsetInstallmentTID=@minTID, @transactionID=@transactionID OUTPUT;

				select @minTID = min(transactionID) from @tblOffsets where transactionID > @minTID;
			END
		END

		-- reduce installmentCount by 1
		IF @AROption = 'A'
			UPDATE dbo.cp_contributions
			set installmentCount = installmentCount - 1
			where contributionID = @contributionID;

		-- all tr_transactionInstallments of this schedule should be set to not active
		UPDATE dbo.tr_transactionInstallments
		set isActive = 0
		where orgID = @orgID 
		and scheduleID = @scheduleID
		and isActive = 1;
	COMMIT TRAN;

	-- process conditions
	IF OBJECT_ID('tempdb..#tblMCQRun') IS NOT NULL 
		DROP TABLE #tblMCQRun;
	CREATE TABLE #tblMCQRun (orgID int, memberID int INDEX IX_tblMCQRun_memberID, conditionID int);

	INSERT INTO #tblMCQRun (orgID, memberID, conditionID)
	select distinct @orgID, @memberID, c.conditionID
	from dbo.ams_virtualGroupConditions as c
	cross apply (
		select cv.conditionValue
		from dbo.ams_virtualGroupConditionValues as cv
		inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'programList'
		where cv.conditionID = c.conditionID
	) as programList(val)
	where c.orgID = @orgID
	and c.fieldCode in ('cp_entry','cp_valuesum')
	and programList.val = cast(@programID as varchar(10));

	EXEC platformQueue.dbo.queue_triggerMCGCache @runImmediately=0, @type='ConditionsAndGroupsChanged';

	IF OBJECT_ID('tempdb..#tblMCQRun') IS NOT NULL 
		DROP TABLE #tblMCQRun;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
