ALTER PROC dbo.email_reviewMessage
@siteID int,
@messageID int,
@recipientID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	declare @messageToParse varchar(max), @memberID int, @emailTo varchar(500);

	select @messageToParse = case when nullif(ltrim(rtrim(m.messagewrapper)),'') is null then cv.rawContent else replace(m.messagewrapper,'@@rawcontent@@',cv.rawContent) end,
		@memberID = mrh.memberID,
		@emailTo = mrh.toEmail + case when len(mrh.toName) > 0 then ' (' + mrh.toName + ')' else '' end
	from dbo.email_messages as m
	inner join membercentral.dbo.cms_contentVersions as cv 
		on m.siteID = @siteID
		and m.messageID = @messageID
		and cv.contentVersionID = m.contentVersionID
	inner join dbo.email_messageRecipientHistory as mrh 
		on mrh.siteID = @siteID 
		and mrh.messageID = @messageID 
		and mrh.recipientID = @recipientID

	-- all merge codes in message
	select distinct f.fieldName
	from dbo.email_messageMetadataFields as mf
	inner join dbo.email_metadataFields as f 
		on mf.messageID = @messageID
		and mf.recipientID = @recipientID
		and mf.fieldID = f.fieldID;

	-- recipient
	select @recipientID as recipientID, @emailTo as emailTo, 
		m.fromName, m.fromEmail, m.[subject], m.dateEntered, @messageToParse as messageContent,
		mt.messageType, mt.allowAdminView
	from dbo.email_messages as m 
	inner join dbo.email_messageTypes as mt 
		on m.siteID = @siteID
		and m.messageID = @messageID
		and mt.messageTypeID = m.messageTypeID;

	-- recipient merge code data
	select @recipientID as recipientID, f.fieldID, f.fieldName, mf.messageid, mf.memberid, mf.fieldValue, mf.fieldTextToReplace
	from dbo.email_messageMetadataFields as mf  
	inner join dbo.email_metadataFields as f 
		  on mf.messageID = @messageID
		  and mf.memberID = @memberID
		  and mf.recipientID = @recipientID
		  and mf.fieldID = f.fieldID
		  and f.isMergeField = 1;


	-- attachments
	select ra.recipientID, ra.attachmentID, att.[fileName], att.localDirectory 
	from dbo.email_messageRecipientAttachments ra
	inner join [dbo].email_attachments att on att.attachmentID = ra.attachmentID
	where ra.recipientID = @recipientID;

	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
