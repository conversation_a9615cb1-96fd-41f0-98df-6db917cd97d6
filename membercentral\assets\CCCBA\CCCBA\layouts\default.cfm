<cfoutput>
	<cfset local.pageTitle = trim(event.getValue( 'mc_pageDefinition.pageTitle',event.getValue( 'mc_pageDefinition.pageName')))/>
	<cfset local.isSideEnabled = false />
	<cfset local.headerBackground = ""/>
	<cfif application.objCMS.getZoneItemCount(zone='L',event=event)>
		<cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['L'],1)>
			<cfset local.headerBackground = trim(REReplace(REReplace(event.getValue("mc_pageDefinition").pageZones['L'][1].data,"<p>",""),"</p>",""))/> 
		</cfif>
	</cfif>
	<cfif len(local.headerBackground) eq 0 and application.objCMS.getZoneItemCount(zone='K',event=event)>
		<cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['K'],1)>
			<cfset local.headerBackground = trim(REReplace(REReplace(event.getValue("mc_pageDefinition").pageZones['K'][1].data,"<p>",""),"</p>",""))/>
		</cfif>
	</cfif>
	<cfif len(local.headerBackground) eq 0>
		<cfset local.headerBackground = '<img src="/images/inner-defaultbanner.jpg"/>'>
	</cfif>
	<cfset local.zoneM1Content="" />
	<cfif application.objCMS.getZoneItemCount(zone='M' ,event=event)>
		<cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['M'],1)>
			<cfset local.zoneM1Content=trim(REReplace(event.getValue( "mc_pageDefinition").pageZones['M'][1].data, "<\/?p[^>]*>", "", "all"))>
		</cfif>
	</cfif>
	<cfset local.zoneN1Content="" />
	<cfif application.objCMS.getZoneItemCount(zone='N' ,event=event)>
		<cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['N'],1)>
			<cfset local.zoneN1Content=trim(REReplace(event.getValue( "mc_pageDefinition").pageZones['N'][1].data, "<\/?p[^>]*>", "", "all"))>
		</cfif>
	</cfif>
	<cfset local.zoneO1Content="" />
	<cfif application.objCMS.getZoneItemCount(zone='O' ,event=event)>
		<cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['O'],1)>
			<cfset local.zoneO1Content=trim(REReplace(event.getValue( "mc_pageDefinition").pageZones['O'][1].data, "<\/?p[^>]*>", "", "all"))>
		</cfif>
	</cfif>
	<cfset local.zoneV1Content="" />
	<cfif application.objCMS.getZoneItemCount(zone='V' ,event=event)>
		<cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['V'],1)>
			<cfset local.zoneV1Content=trim(REReplace(event.getValue( "mc_pageDefinition").pageZones['V'][1].data, "<\/?p[^>]*>", "", "all"))>
		</cfif>
	</cfif>
	<cfset local.zoneX1Content="" />
	<cfif application.objCMS.getZoneItemCount(zone='X' ,event=event)>
		<cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['X'],1)>
			<cfset local.zoneX1Content=trim(REReplace(event.getValue( "mc_pageDefinition").pageZones['X'][1].data, "<\/?p[^>]*>", "", "all"))>
		</cfif>
	</cfif>
	<cfif len(local.zoneM1Content) OR len(local.zoneN1Content) OR len(local.zoneO1Content) OR len(local.zoneX1Content)>
		<cfset local.isSideEnabled=true /> 
	</cfif>
	
	<!doctype html>
	<html lang="en">
		<head>
			<cfinclude template="head.cfm">		
		</head>
		<cfif event.getValue('mc_pageDefinition.layoutMode','normal') neq "direct">
			<body>
				<div class="wrapper">
					<cfinclude template="header.cfm">
	
					<!-- Inner Banner Start -->
					<div class="bannerInner">
						<img src="/images/inner-banner-bg.png" class="fixed-bg inner-banner-bg" />
						#local.headerBackground#
						<div class="banner-content">
							<div class="container containerCustom">
								<h1 class="TitleText">#local.pageTitle#</h1>
							</div>
						</div>
					</div>
					<!-- Inner Banner End -->
					
					<!-- Main Content Start -->
					<div class="content">
						<div class="inner-page-content">
							<div class="container containerCustom">
								<div class="row-fluid">
									<div class="inner-content-area <cfif local.isSideEnabled and event.getValue('mc_pageDefinition.layoutMode','normal') neq "full"> span8<cfelse> span12</cfif>" 
										<cfif local.isSideEnabled and event.getValue('mc_pageDefinition.layoutMode','normal') neq "full"> style="width: calc(100% - 325px);" style="padding: 60px 30px 0px 0px;"<cfelse> style="padding: 60px 0px 0px 0px"</cfif>>
										<cfif application.objCMS.getZoneItemCount(zone='Main' ,event=event)> #application.objCMS.renderZone(zone='Main',event=event)# </cfif>
									</div>
									<cfif local.isSideEnabled and event.getValue( 'mc_pageDefinition.layoutMode', 'normal') neq "full">
										<div class="span4 sidebar hidden-phone hidden-tablet">
											<span class="zoneM1Holder"></span>
											<span class="zoneM1Wrapper hide">#local.zoneM1Content#</span>
											
											<span class="zoneN1Holder"></span>
											<span class="zoneN1Wrapper hide">#local.zoneN1Content#</span>

											<span class="zoneO1Holder"></span>
											<span class="zoneO1Wrapper hide">#local.zoneO1Content#</span>
											
											<cfif len(local.zoneX1Content)>
											<div class="adv-block">
												#local.zoneX1Content#
											</div>
											</cfif>
										</div>
									</cfif>
								</div>
							</div>
						</div>
					</div>
					<!-- Main Content End -->
					
					<span id="tableLayoutWrapper"></span>
					<span class="zoneV1Holder"></span>
					<span class="zoneV1Wrapper hide">#local.zoneV1Content#</span>

					<div class="visible-phone visible-tablet pdb_40">
						<div class="container containerCustom px-15">
							<div class="sidebar">
								<span class="zoneM1Holder"></span>
								<span class="zoneN1Holder"></span>
								<span class="zoneO1Holder"></span>
								<cfif len(local.zoneX1Content)>
									<div class="adv-block">
										#local.zoneX1Content#
									</div>
								</cfif>
							</div>
						</div>
					</div>

					<cfif len(local.zoneV1Content)>
						<!-- FriendsLogoBox Section Start -->
						<div class="sponsors-sec">
							<div class="container containerCustom">
								<div class="row flex-row">
									<div class="span12">
										<h2 class="SectionHeader text-center sponsorSliderHolder"></h2>
									</div>
									<div class="span12">
										<div class="sponsorSlider sponsorSliderWrapper">
											#local.zoneV1Content#
										</div>
									</div>
								</div>
							</div>
						</div>
					</cfif>
				<!--Content End-->
				<cfinclude template="footer.cfm">
				<cfinclude template="toolBar.cfm">
			</div>
		</body>
	<cfelse>
		<body style="background:none!important;background:unset!important;padding:20px;" class="innerPage-content">
			#application.objCMS.renderZone(zone='Main',event=event)#
		</body>		
	</cfif>
	<cfinclude template="foot.cfm">
</html>
</cfoutput>