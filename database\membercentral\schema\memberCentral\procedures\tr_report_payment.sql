ALTER PROC dbo.tr_report_payment
@orgID int,
@datetype varchar(15),
@startDate datetime,
@endDate datetime,
@profileID int,
@walletTypesList varchar(15),
@lowamt decimal(18,2),
@highamt decimal(18,2),
@batch varchar(400),
@includeAllocs bit,
@pending bit,
@fld4 varchar(4),
@fld10 varchar(4),
@fld18 varchar(20),
@fld19 varchar(20)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	-- if pending = 0, status 3 (pending) do not appear here -- they are not accepted transactions
	-- if pending = 1, status 3 appears here and not 1,2
	-- status 4 (voidedpending) do not appear here -- they are meant to be completely hidden (deprecated)
	-- status 2 (voided) do not appear here

	if @profileID = 0
		select @profileID = null;

	if (@lowamt = @highamt and @highamt = 0) or @highamt = 0 begin
		set @lowamt = null;
		set @highamt = null;
	end

	declare @payStatuses TABLE (statusid int);
	declare @tblWalletTypes TABLE ([type] varchar(5));

	if @pending = 0
		insert into @payStatuses (statusid)
		values (1);
	else
		insert into @payStatuses (statusid)
		values (3);

	IF NULLIF(@walletTypesList,'') IS NOT NULL
		INSERT INTO @tblWalletTypes ([type])
		SELECT listitem
		FROM dbo.fn_varcharListToTable(@walletTypesList,',');

	declare @t_Allocation int, @tr_RefundTrans int, @tr_DITSaleTrans int, @tr_AllocPayTrans int, 
		@tr_WriteOffPayTrans int, @tr_NSFTrans int;
	set @t_Allocation = dbo.fn_tr_getTypeID('Allocation');
	set @tr_RefundTrans = dbo.fn_tr_getRelationshipTypeID('RefundTrans');
	set @tr_DITSaleTrans = dbo.fn_tr_getRelationshipTypeID('DITSaleTrans');
	set @tr_AllocPayTrans = dbo.fn_tr_getRelationshipTypeID('AllocPayTrans');
	set @tr_WriteOffPayTrans = dbo.fn_tr_getRelationshipTypeID('WriteOffPayTrans');
	set @tr_NSFTrans = dbo.fn_tr_getRelationshipTypeID('NSFTrans');

	-- set date to 11:59:59 of enddate
	if @endDate is not null
		select @endDate = dateadd(s,-1,dateadd(day,1,DATEADD(dd, DATEDIFF(dd,0,@endDate), 0)));

	IF OBJECT_ID('tempdb..#tblTrans') IS NOT NULL 
		DROP TABLE #tblTrans;
	IF OBJECT_ID('tempdb..#tblTransResult') IS NOT NULL 
		DROP TABLE #tblTransResult;
	CREATE TABLE #tblTrans (rootTransactionID int, transactionID int, typeID int);
	CREATE TABLE #tblTransResult (rootTransactionID int, [type] varchar(20), amount decimal(18,2), transactionDate varchar(10), dateRecorded datetime,
		Detail varchar(max), DEBITACCOUNT varchar(max), DEBITACCOUNTCODE varchar(200), CREDITACCOUNT varchar(max), CREDITACCOUNTCODE varchar(200),
		assignedToMemberID int, assignedToMember varchar(max), assignedToCompany varchar(200), assignedToMemberNumber varchar(200), [Invoice Number] varchar(50),
		[Invoice Date Billed] datetime, [Invoice Date Due] datetime, isRoot bit);

	-- payments / refunds directly found
	insert into #tblTrans (rootTransactionID, transactionID, typeid)
	select t.transactionID, t.transactionID, t.typeid
	from dbo.tr_transactions as t
	inner join dbo.tr_transactionPayments as tp on tp.orgID = @orgID and tp.transactionID = t.transactionID
	inner join dbo.mp_profiles as mp on mp.profileID = tp.profileID
	inner join dbo.mp_gateways as mg on mg.gatewayID = mp.gatewayID
	inner join dbo.tr_batchTransactions as bt on bt.orgID = @orgID and bt.transactionID = t.transactionID
	inner join dbo.tr_batches as b on b.orgID = @orgID and b.batchID = bt.batchID
	inner join @payStatuses as ps on ps.statusID = t.statusID
	inner join dbo.tr_paymentHistory as ph on ph.orgID = @orgID and ph.historyID = tp.historyID
	inner join dbo.tr_GLAccounts as glPay on glPay.orgID = @orgID and glPay.GLAccountID = t.debitGLAccountID and glPay.status <> 'D'
	inner join dbo.tr_GLAccounts as glRef on glRef.orgID = @orgID and glRef.GLAccountID = t.creditGLAccountID and glRef.status <> 'D'
	where (@profileID is null or tp.profileID = @profileID)
	and (@lowamt is null or t.amount between @lowamt and @highamt)
	and t.ownedByOrgID = @orgID
	and (
		(@datetype = 'dateRecorded' and t.dateRecorded between @startdate and @endDate)
		or
		(@datetype = 'transactionDate' and t.transactionDate between @startdate and @endDate)
	)
	and (
		NULLIF(@walletTypesList,'') IS NULL
		OR EXISTS (
			SELECT 1
			FROM @tblWalletTypes AS tmp
			WHERE (tmp.[type] = 'NWC' AND tp.isApplePay = 0 AND tp.isGooglePay = 0)
			   OR (tmp.[type] = 'AP' AND tp.isApplePay = 1)
			   OR (tmp.[type] = 'GP' AND tp.isGooglePay = 1)
		)

	)
	and (@batch is null or b.batchName like '%' + @batch + '%')
	and (
		@fld4 is null 
		or 
		(mp.gatewayID in (1,3,7,9,10,12,17) and 
			(right(ph.paymentInfo.value('(//args/fld_4_)[1]','varchar(40)'),4) = @fld4
			or
			right(t.detail,4) = @fld4
			or
			right(t.detail,4) = @fld4)
		)
		)
	and (
		@fld10 is null 
		or 
		(mg.tokenStore = 'bankdraft' and 
			(right(ph.paymentInfo.value('(//gateway/fld_10_)[1]','varchar(40)'),4) = @fld10
			or
			right(t.detail,8) = 'XXXX' + @fld10)
		)
		)
	and (@fld18 is null or ph.paymentInfo.value('(//args/fld_18_)[1]','varchar(30)') = @fld18)
	and (@fld19 is null or ph.paymentInfo.value('(//args/fld_19_)[1]','varchar(30)') = @fld19);

	-- all payments tied to refunds
	insert into #tblTrans (rootTransactionID, transactionID, typeid)
	select tbl.rootTransactionID, tr.appliedToTransactionID, t.typeID
	from #tblTrans as tbl
	inner join dbo.tr_relationships as tr on tr.orgID = @orgID and tr.typeID = @tr_RefundTrans and tr.TransactionID = tbl.transactionID
	inner join dbo.tr_transactions as t on t.ownedByOrgID = @orgID and t.transactionID = tr.appliedToTransactionID
	where tbl.typeID = 4
	and t.statusID in (1,3);

	-- allocations, refunds, write offs, nsf trans applied to payments
	insert into #tblTrans (rootTransactionID, transactionID, typeid)
	select tbl.rootTransactionID, t.transactionid, t.typeID
	from #tblTrans as tbl
	inner join dbo.tr_relationships as tr on tr.orgID = @orgID and tr.typeID in (@tr_AllocPayTrans,@tr_RefundTrans,@tr_WriteOffPayTrans,@tr_NSFTrans) and tr.appliedToTransactionID = tbl.transactionID
	inner join dbo.tr_transactions as t on t.ownedByOrgID = @orgID and t.transactionID = tr.transactionid
	where tbl.typeID = 2
	and tbl.rootTransactionID = tbl.transactionID
	and t.statusID in (1,3);

	declare @tblGL TABLE (GLAccountID int, accountCode varchar(200), glCode varchar(50), thePathExpanded varchar(max));
	insert into @tblGL (GLAccountID, accountCode, glCode, thePathExpanded)
	SELECT rgl.GLAccountID, rgl.accountCode, gl.glCode, rgl.thePathExpanded
	FROM dbo.fn_getRecursiveGLAccounts(@orgID) as rgl
	inner join dbo.tr_GLAccounts as gl on gl.orgID = @orgID and gl.glaccountID = rgl.glaccountID
	where gl.status <> 'D';

	INSERT INTO #tblTransResult (rootTransactionID, [type], amount, transactionDate, dateRecorded,
		Detail, DEBITACCOUNT, DEBITACCOUNTCODE, CREDITACCOUNT, CREDITACCOUNTCODE,
		assignedToMemberID, assignedToMember, assignedToCompany, assignedToMemberNumber,
		[Invoice Number], [Invoice Date Billed], [Invoice Date Due], isRoot)
	select tmp.rootTransactionID, tt.type, t.amount, convert(varchar(10),t.transactionDate,101) as transactionDate, t.dateRecorded, 
		case when tt.type = 'Write Off' then 'Write-Off of ' + t.detail else t.detail end as Detail,
		rglDeb.thePathExpanded as DEBITACCOUNT,
		isnull(rglDeb.accountCode,'') as DEBITACCOUNTCODE,
		rglCred.thePathExpanded as CREDITACCOUNT,
		isnull(rglCred.accountCode,'') as CREDITACCOUNTCODE,
		m2.memberID as assignedToMemberID,
		m2.lastname + ', ' + m2.firstname as assignedToMember,
		m2.company as assignedToCompany,
		m2.membernumber as assignedToMemberNumber,
		'' as [Invoice Number], 
		null as [Invoice Date Billed], 
		null as [Invoice Date Due],
		case when tmp.rootTransactionID = tmp.transactionID then 1 else 0 end as isRoot
	from #tblTrans as tmp
	inner join dbo.tr_transactions as t on t.ownedByOrgID = @orgID and t.typeID <> @t_Allocation and t.transactionID = tmp.transactionID
	inner join dbo.tr_types as tt on tt.typeID = t.typeID
	inner join dbo.ams_members as m on m.orgID = @orgID and m.memberid = t.assignedToMemberID
	inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberid = m.activeMemberID
	INNER JOIN @tblGL as rglDeb on rglDeb.GLAccountID = t.debitGLAccountID
	INNER JOIN @tblGL as rglCred on rglCred.GLAccountID = t.creditGLAccountID;

	IF @includeAllocs = 1 BEGIN
		INSERT INTO #tblTransResult (rootTransactionID, [type], amount, transactionDate, dateRecorded,
			Detail, DEBITACCOUNT, DEBITACCOUNTCODE, CREDITACCOUNT, CREDITACCOUNTCODE,
			assignedToMemberID, assignedToMember, assignedToCompany, assignedToMemberNumber,
			[Invoice Number], [Invoice Date Billed], [Invoice Date Due], isRoot)
		select tmp.rootTransactionID,  
			case when rglDeb.GLCode = 'ACCOUNTSRECEIVABLE' then 'Deallocation' else 'Allocation' end,
			sum(alloc.amount_alloc), convert(varchar(10),t.transactionDate,101), min(t.dateRecorded), 
			case 
				when rglDeb.GLCode = 'ACCOUNTSRECEIVABLE' then 'Deallocation from ' + isnull(tSaleDit.detail,'')
				else 'Allocation to ' + isnull(tSaleDit.detail,'')
				end,
			case when rglDeb.GLCode = 'ACCOUNTSRECEIVABLE' then rglCredSale.thePathExpanded else rglDeb.thePathExpanded end,
			case when rglDeb.GLCode = 'ACCOUNTSRECEIVABLE' then isnull(rglCredSale.accountCode,'') else isnull(rglDeb.accountCode,'') end,
			case when rglCred.GLCode = 'ACCOUNTSRECEIVABLE' then rglCredSale.thePathExpanded else rglCred.thePathExpanded end,
			case when rglCred.GLCode = 'ACCOUNTSRECEIVABLE' then isnull(rglCredSale.accountCode,'') else isnull(rglCred.accountCode,'') end,
			mSale2.memberID, mSale2.lastname + ', ' + mSale2.firstname, mSale2.company, mSale2.membernumber,
			o.orgcode + dbo.fn_tr_padInvoiceNumber(i.invoiceNumber), i.dateBilled, i.dateDue,
			0 as isRoot
		from #tblTrans as tmp
		inner join dbo.cache_tr_allocations as alloc on alloc.orgID = @orgID and alloc.transactionID_alloc = tmp.transactionID
		inner join dbo.tr_transactions as t on t.ownedByOrgID = @orgID and t.transactionID = alloc.transactionID_alloc
		inner join dbo.ams_members as m on m.orgID = @orgID and m.memberid = alloc.assignedToMemberID_alloc
		inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberid = m.activeMemberID
		INNER JOIN @tblGL as rglDeb on rglDeb.GLAccountID = alloc.debitGLAccountID_alloc
		INNER JOIN @tblGL as rglCred on rglCred.GLAccountID = alloc.creditGLAccountID_alloc
		inner join dbo.tr_transactions as tSaleDit on tSaleDit.ownedByOrgID = @orgID and tSaleDit.transactionID = alloc.transactionID_rev and tSaleDit.statusID = 1 and tSaleDit.typeID in (1,3,7)
		inner join dbo.ams_members as mSale on mSale.orgID = @orgID and mSale.memberid = tSaleDit.assignedToMemberID
		inner join dbo.ams_members as mSale2 on mSale2.orgID = @orgID and mSale2.memberid = mSale.activeMemberID
		INNER JOIN @tblGL as rglCredSale on rglCredSale.GLAccountID = tSaleDit.creditGLAccountID
		inner join dbo.tr_invoiceTransactions as it on it.orgID = @orgID and it.transactionID = tSaleDit.transactionID
		inner join dbo.tr_invoices as i on i.orgID = @orgID and i.invoiceID = it.invoiceID
		inner join dbo.organizations as o on o.orgID = tSaleDit.ownedByOrgID
		group by tmp.rootTransactionID,  
			case when rglDeb.GLCode = 'ACCOUNTSRECEIVABLE' then 'Deallocation' else 'Allocation' end,
			convert(varchar(10),t.transactionDate,101), 
			case when rglDeb.GLCode = 'ACCOUNTSRECEIVABLE' then 'Deallocation from ' + isnull(tSaleDit.detail,'') else 'Allocation to ' + isnull(tSaleDit.detail,'') end,
			case when rglDeb.GLCode = 'ACCOUNTSRECEIVABLE' then rglCredSale.thePathExpanded else rglDeb.thePathExpanded end,
			case when rglDeb.GLCode = 'ACCOUNTSRECEIVABLE' then isnull(rglCredSale.accountCode,'') else isnull(rglDeb.accountCode,'') end,
			case when rglCred.GLCode = 'ACCOUNTSRECEIVABLE' then rglCredSale.thePathExpanded else rglCred.thePathExpanded end,
			case when rglCred.GLCode = 'ACCOUNTSRECEIVABLE' then isnull(rglCredSale.accountCode,'') else isnull(rglCred.accountCode,'') end,
			mSale2.memberID, mSale2.lastname + ', ' + mSale2.firstname, mSale2.company, mSale2.membernumber,
			o.orgcode + dbo.fn_tr_padInvoiceNumber(i.invoiceNumber), i.dateBilled, i.dateDue
			union all
		select tmp.rootTransactionID,  
			case when rglDeb.GLCode = 'ACCOUNTSRECEIVABLE' then 'Deallocation' else 'Allocation' end,
			sum(alloc.amount_alloc), convert(varchar(10),t.transactionDate,101), min(t.dateRecorded), 
			case 
				when rglDeb.GLCode = 'ACCOUNTSRECEIVABLE' then 'Deallocation from ' + isnull(tSaleAdj.detail,'')
				else 'Allocation to ' + isnull(tSaleAdj.detail,'')
				end,
			case when rglDeb.GLCode = 'ACCOUNTSRECEIVABLE' then rglCredSale.thePathExpanded else rglDeb.thePathExpanded end,
			case when rglDeb.GLCode = 'ACCOUNTSRECEIVABLE' then isnull(rglCredSale.accountCode,'') else isnull(rglDeb.accountCode,'') end,
			case when rglCred.GLCode = 'ACCOUNTSRECEIVABLE' then rglCredSale.thePathExpanded else rglCred.thePathExpanded end,
			case when rglCred.GLCode = 'ACCOUNTSRECEIVABLE' then isnull(rglCredSale.accountCode,'') else isnull(rglCred.accountCode,'') end,
			mSale2.memberID, mSale2.lastname + ', ' + mSale2.firstname, mSale2.company, mSale2.membernumber,
			o.orgcode + dbo.fn_tr_padInvoiceNumber(i.invoiceNumber), i.dateBilled, i.dateDue,
			0 as isRoot
		from #tblTrans as tmp
		inner join dbo.cache_tr_allocations as alloc on alloc.orgID = @orgID and alloc.transactionID_alloc = tmp.transactionID
		inner join dbo.tr_transactions as t on t.ownedByOrgID = @orgID and t.transactionID = alloc.transactionID_alloc
		inner join dbo.ams_members as m on m.orgID = @orgID and m.memberid = alloc.assignedToMemberID_alloc
		inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberid = m.activeMemberID
		INNER JOIN @tblGL as rglDeb on rglDeb.GLAccountID = alloc.debitGLAccountID_alloc
		INNER JOIN @tblGL as rglCred on rglCred.GLAccountID = alloc.creditGLAccountID_alloc
		inner join dbo.tr_transactions as tSaleDit on tSaleDit.ownedByOrgID = @orgID and tSaleDit.transactionID = alloc.transactionID_rev and tSaleDit.statusID = 1
		inner join dbo.ams_members as mSale on mSale.orgID = @orgID and mSale.memberid = tSaleDit.assignedToMemberID
		inner join dbo.ams_members as mSale2 on mSale2.orgID = @orgID and mSale2.memberid = mSale.activeMemberID
		inner join dbo.tr_relationships as rDit on rDit.orgID = @orgID and rDit.typeID = @tr_DITSaleTrans and rDit.transactionID = tSaleDit.transactionID
		inner join dbo.tr_transactions as tSaleAdj on tSaleAdj.ownedByOrgID = @orgID and tSaleAdj.transactionID = rDit.appliedToTransactionID and tSaleAdj.statusID = 1
		INNER JOIN @tblGL as rglCredSale on rglCredSale.GLAccountID = tSaleDit.creditGLAccountID
		inner join dbo.tr_invoiceTransactions as it on it.orgID = @orgID and it.transactionID = rDit.appliedToTransactionID
		inner join dbo.tr_invoices as i on i.orgID = @orgID and i.invoiceID = it.invoiceID
		inner join dbo.organizations as o on o.orgID = tSaleDit.ownedByOrgID
		group by tmp.rootTransactionID, case when rglDeb.GLCode = 'ACCOUNTSRECEIVABLE' then 'Deallocation' else 'Allocation' end,
			convert(varchar(10),t.transactionDate,101), 
			case when rglDeb.GLCode = 'ACCOUNTSRECEIVABLE' then 'Deallocation from ' + isnull(tSaleAdj.detail,'') else 'Allocation to ' + isnull(tSaleAdj.detail,'') end,
			case when rglDeb.GLCode = 'ACCOUNTSRECEIVABLE' then rglCredSale.thePathExpanded else rglDeb.thePathExpanded end,
			case when rglDeb.GLCode = 'ACCOUNTSRECEIVABLE' then isnull(rglCredSale.accountCode,'') else isnull(rglDeb.accountCode,'') end,
			case when rglCred.GLCode = 'ACCOUNTSRECEIVABLE' then rglCredSale.thePathExpanded else rglCred.thePathExpanded end,
			case when rglCred.GLCode = 'ACCOUNTSRECEIVABLE' then isnull(rglCredSale.accountCode,'') else isnull(rglCred.accountCode,'') end,
			mSale2.memberID, mSale2.lastname + ', ' + mSale2.firstname, mSale2.company, mSale2.membernumber,
			o.orgcode + dbo.fn_tr_padInvoiceNumber(i.invoiceNumber), i.dateBilled, i.dateDue;
	END

	SELECT rootTransactionID, [type], amount, transactionDate, dateRecorded,
		Detail, DEBITACCOUNT, DEBITACCOUNTCODE, CREDITACCOUNT, CREDITACCOUNTCODE,
		assignedToMemberID, assignedToMember, assignedToCompany, assignedToMemberNumber,
		[Invoice Number], [Invoice Date Billed], [Invoice Date Due], isRoot
	FROM #tblTransResult
	order by rootTransactionID, isRoot desc, dateRecorded;

	IF OBJECT_ID('tempdb..#tblTrans') IS NOT NULL 
		DROP TABLE #tblTrans;

	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
