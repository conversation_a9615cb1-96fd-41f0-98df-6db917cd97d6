ALTER PROC dbo.cp_updatePaymentProfile
@siteID int,
@contributionID int,
@MPProfileID int,
@payProfileID int,
@payProcessFee bit,
@retainCurrentFeePercent bit,
@recordedByMemberID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpAuditLog') IS NOT NULL 
		DROP TABLE #tmpAuditLog;
	IF OBJECT_ID('tempdb..#tmpUpdateInvoices') IS NOT NULL 
		DROP TABLE #tmpUpdateInvoices;
	CREATE TABLE #tmpAuditLog (auditCode varchar(10), msg varchar(max));
	CREATE TABLE #tmpUpdateInvoices (invoiceID int);

	DECLARE @orgID int, @existingPayProfileID int, @existingPayProcessFee bit, @existingProcessFeePercent decimal(5,2), 
		@processFeePercent decimal(5,2), @contributorAndProgramInfo varchar(625), @payProfileIDList varchar(max);
	
	SELECT @orgID = dbo.fn_getOrgIDFromSiteID(@siteID);

	SET @payProfileID = NULLIF(@payProfileID,0);
	SET @payProcessFee = ISNULL(@payProcessFee,0);

	SELECT TOP 1 @existingPayProfileID = payProfileID, @existingPayProcessFee = payProcessFee, @existingProcessFeePercent = processFeePercent
	FROM dbo.cp_contributionPayProfiles
	WHERE contributionID = @contributionID;

	SELECT @contributorAndProgramInfo = '[' + cp.programName + ISNULL(' - ' + cpc.campaignName,'') + '] (ContributionID: ' + CAST(c.contributionID AS varchar(10)) + ')'
	FROM dbo.cp_contributions AS c
	INNER JOIN dbo.cp_programs AS cp ON cp.programID = c.programID
	LEFT OUTER JOIN dbo.cp_campaigns AS cpc ON cpc.campaignID = c.campaignID
	WHERE c.contributionID = @contributionID;

	IF @payProcessFee = 1 BEGIN
		IF @retainCurrentFeePercent = 1
			SELECT @processFeePercent = processFeePercent
			FROM dbo.cp_contributionPayProfiles
			WHERE contributionID = @contributionID;
		ELSE
			SELECT @processFeePercent = processFeeDonationFeePercent
			FROM dbo.mp_profiles
			WHERE profileID = @MPProfileID
			AND enableProcessingFeeDonation = 1;
	END

	IF ISNULL(@existingPayProfileID,0) <> ISNULL(@payProfileID,0)
		INSERT INTO #tmpAuditLog (auditCode, msg)
		SELECT 'CP', 'Pay Profile ' +
				CASE WHEN mpp.payProfileID IS NOT NULL AND mpp2.payProfileID IS NOT NULL THEN 'changed from ' + mpp.detail + ' to ' + mpp2.detail + ' for'
					WHEN mpp.payProfileID IS NOT NULL AND mpp2.payProfileID IS NULL THEN mpp.detail + ' removed from '
					WHEN mpp.payProfileID IS NULL AND mpp2.payProfileID IS NOT NULL THEN mpp2.detail + ' associated to '
				END + @contributorAndProgramInfo
		FROM dbo.cp_contributions as c
		LEFT OUTER JOIN dbo.ams_memberPaymentProfiles as mpp on mpp.payProfileID = @existingPayProfileID
		LEFT OUTER JOIN dbo.ams_memberPaymentProfiles as mpp2 on mpp2.payProfileID = @payProfileID
		WHERE c.contributionID = @contributionID;

	IF ISNULL(@existingPayProcessFee,0) <> @payProcessFee
		INSERT INTO #tmpAuditLog (auditCode, msg)
		SELECT 'CP', 'Pay Processing Fees changed from ' + CASE WHEN ISNULL(@existingPayProcessFee,0) = 1 THEN 'Yes' ELSE 'No' END  + ' to ' + CASE WHEN @payProcessFee = 1 THEN 'Yes' ELSE 'No' END + ' for ' + @contributorAndProgramInfo;

	IF ISNULL(@existingProcessFeePercent,0) <> ISNULL(@processFeePercent ,0)
		INSERT INTO #tmpAuditLog (auditCode, msg)
		SELECT 'CP', 'Processing Fee Percentage changed from ' + CAST(ISNULL(@existingProcessFeePercent ,0) AS varchar(10)) + '% to ' + CAST(ISNULL(@processFeePercent ,0) AS varchar(10)) + '% for ' + @contributorAndProgramInfo;

	IF ISNULL(@existingPayProfileID,0) <> ISNULL(@payProfileID,0) OR ISNULL(@existingPayProcessFee,0) <> @payProcessFee BEGIN
		-- inv
		INSERT INTO #tmpUpdateInvoices (invoiceID)
		SELECT DISTINCT tri.invoiceID
		FROM dbo.fn_cp_contributionTransactions(@contributionID) AS ct
		INNER JOIN dbo.tr_invoiceTransactions AS it ON it.orgID = @orgID
			AND it.transactionID = ct.transactionID
		INNER JOIN dbo.tr_invoices AS tri ON tri.orgID = @orgID
			AND tri.invoiceID = it.invoiceID
		INNER JOIN dbo.tr_invoiceStatuses AS invs ON invs.statusID = tri.statusID
		WHERE invs.status IN ('Open','Closed','Delinquent')
		AND (
			ISNULL(tri.payProfileID,0) <> ISNULL(@payProfileID,0) 
			OR tri.payProcessFee <> @payProcessFee
		);

		IF ISNULL(@existingPayProfileID,0) <> ISNULL(@payProfileID,0)
			INSERT INTO #tmpAuditLog (auditCode, msg)
			SELECT 'INV', 'Pay Profile ' + 
					CASE WHEN mpp.payProfileID IS NOT NULL AND mpp2.payProfileID IS NOT NULL THEN 'changed from ' + mpp.detail + ' to ' + mpp2.detail + ' for'
						WHEN mpp.payProfileID IS NOT NULL AND mpp2.payProfileID IS NULL THEN mpp.detail + ' removed from'
						WHEN mpp.payProfileID IS NULL AND mpp2.payProfileID IS NOT NULL THEN mpp2.detail + ' associated to'
					END + ' Invoice ' + o.orgCode + dbo.fn_tr_padInvoiceNumber(i.invoiceNumber)
			FROM dbo.tr_invoices as i
			INNER JOIN #tmpUpdateInvoices as tmp on tmp.invoiceID = i.invoiceID
			INNER JOIN dbo.organizations as o on o.orgID = i.orgID
			LEFT OUTER JOIN dbo.ams_memberPaymentProfiles as mpp on mpp.payProfileID = i.payProfileID
			LEFT OUTER JOIN dbo.ams_memberPaymentProfiles as mpp2 on mpp2.payProfileID = @payProfileID
			WHERE i.orgID = @orgID;

		IF ISNULL(@existingPayProcessFee,0) <> @payProcessFee BEGIN
			INSERT INTO #tmpAuditLog (auditCode, msg)
			SELECT 'INV', 'Pay Processing Fees changed from ' + CASE WHEN i.payProcessFee = 1 THEN 'Yes' ELSE 'No' END  + ' to ' + CASE WHEN @payProcessFee = 1 THEN 'Yes' ELSE 'No' END + ' for Invoice ' + o.orgCode + dbo.fn_tr_padInvoiceNumber(i.invoiceNumber)
			FROM dbo.tr_invoices as i
			INNER JOIN #tmpUpdateInvoices as tmp on tmp.invoiceID = i.invoiceID
			INNER JOIN dbo.organizations as o on o.orgID = i.orgID
			WHERE i.payProcessFee <> @payProcessFee;

			INSERT INTO #tmpAuditLog (auditCode, msg)
			SELECT 'INV', 'Processing Fee Percentage changed from ' + CAST(ISNULL(i.processFeePercent ,0) AS varchar(10)) + '% to ' + CAST(ISNULL(@processFeePercent ,0) AS varchar(10)) + '% for Invoice ' + o.orgCode + dbo.fn_tr_padInvoiceNumber(i.invoiceNumber)
			FROM dbo.tr_invoices as i
			INNER JOIN #tmpUpdateInvoices as tmp on tmp.invoiceID = i.invoiceID
			INNER JOIN dbo.organizations as o on o.orgID = i.orgID
			WHERE ISNULL(i.processFeePercent,0) <> ISNULL(@processFeePercent,0);
		END

		BEGIN TRAN;
			-- remove pay profile
			IF @payProfileID IS NULL
				DELETE FROM dbo.cp_contributionPayProfiles
				WHERE contributionID = @contributionID;

			-- add pay profile
			IF @existingPayProfileID IS NULL AND @payProfileID IS NOT NULL BEGIN
				INSERT INTO dbo.cp_contributionPayProfiles (contributionID, MPProfileID, payProfileID, invoiceProfileID, payProcessFee, processFeePercent)
				select distinct @contributionID, @MPProfileID, @payProfileID, invoiceProfileID, @payProcessFee, @processFeePercent
				from (
					select distinct ip.profileID as invoiceProfileID
					from dbo.cp_distributions as cpd
					inner join dbo.cp_programs as cp on cp.programID = cpd.programID
					inner join dbo.cp_contributions as c on c.programID = cp.programID
					inner join dbo.tr_GLAccounts as gl on gl.GLAccountID = cpd.GLAccountID
					inner join dbo.tr_invoiceProfiles as ip on ip.profileID = gl.invoiceProfileID
					where c.contributionID = @contributionID
						union all
					select distinct ip.profileID as invoiceProfileID
					from dbo.cf_fieldData as fd
					inner join dbo.cf_fields as f on f.fieldID = fd.fieldID
					inner join dbo.cf_fieldTypes as ft on ft.fieldTypeID = f.fieldTypeID
					inner join dbo.cp_programs as p on p.programID = f.detailID and f.controllingSiteResourceID = p.siteResourceID
					inner join dbo.tr_GLAccounts as gl on gl.GLAccountID = isnull(f.GLAccountID,p.defaultGLAccountID)
					inner join dbo.tr_invoiceProfiles as ip on ip.profileID = gl.invoiceProfileID
					left outer join dbo.cf_fieldValues as fv on fv.fieldID = f.fieldID and fv.valueID = fd.valueID
						and ft.displayTypeCode in ('SELECT','RADIO','CHECKBOX')
					where fd.itemID = @contributionID
					and fd.itemType = 'ContributionProgram'
					and f.isActive = 1
					and case 
						when ft.displayTypeCode = 'TEXTBOX' and ( (ft.supportQty = 1 and f.amount > 0) or ft.supportAmt = 1 )  then 1
						when ft.displayTypeCode in ('SELECT','RADIO','CHECKBOX') and fv.amount > 0 then 1
					else 0 end = 1
				) tmp;
			END

			-- update cp pay profile settings
			IF @existingPayProfileID IS NOT NULL AND @payProfileID IS NOT NULL AND (@existingPayProfileID <> @payProfileID OR ISNULL(@existingPayProcessFee,0) <> @payProcessFee)
				UPDATE dbo.cp_contributionPayProfiles
				SET payProfileID = @payProfileID,
					MPProfileID = @MPProfileID,
					payProcessFee = @payProcessFee,
					processFeePercent = @processFeePercent
				WHERE contributionID = @contributionID;

			-- trigger reprocessing of credit card expiration conditions (if limiting to contributions)
			IF ISNULL(@existingPayProfileID,0) <> ISNULL(@payProfileID,0) BEGIN
				SET @payProfileIDList = CAST(ISNULL(@existingPayProfileID,0) as varchar(20)) + ',' + CAST(ISNULL(@payProfileID,0) as varchar(20));
				EXEC dbo.tr_reprocessCCExpConditions @orgID=@orgID, @payProfileIDList=@payProfileIDList, @lookupMode='limittocp';
			END

			-- update invoice pay profile settings
			IF EXISTS (SELECT 1 FROM #tmpUpdateInvoices)
				UPDATE tri
				SET tri.payProfileID = @payProfileID,
					tri.MPProfileID = @MPProfileID,
					tri.payProcessFee = @payProcessFee,
					tri.processFeePercent = @processFeePercent
				FROM dbo.tr_invoices as tri
				INNER JOIN #tmpUpdateInvoices as tmp on tmp.invoiceID = tri.invoiceID;

			-- auditLog
			IF EXISTS (SELECT 1 FROM #tmpAuditLog) BEGIN
				INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
				SELECT ('{ "c":"auditLog", "d": {
					"AUDITCODE":"'+ auditCode +'",
					"ORGID":' + cast(@orgID as varchar(10)) + ',
					"SITEID":' + cast(@siteID as varchar(10)) + ',
					"ACTORMEMBERID":' + cast(@recordedByMemberID as varchar(20)) + ',
					"ACTIONDATE":"' + convert(varchar(20),GETDATE(),120) + '",
					"MESSAGE":"' + replace(dbo.fn_cleanInvalidXMLChars(msg),'"','\"') + '" } }')
				FROM #tmpAuditLog;
			END
		COMMIT TRAN;
	END

	IF OBJECT_ID('tempdb..#tmpAuditLog') IS NOT NULL 
		DROP TABLE #tmpAuditLog;
	IF OBJECT_ID('tempdb..#tmpUpdateInvoices') IS NOT NULL 
		DROP TABLE #tmpUpdateInvoices;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
