ALTER PROC dbo.tr_createTransaction_adjustment_up
@recordedOnSiteID int,
@recordedByMemberID int,
@statsSessionID int,
@amount decimal(18,2),
@taxAmount decimal(18,2),
@transactionDate datetime,
@autoAdjustTransactionDate bit,
@saleTransactionID int,
@invoiceID int,
@byPassTax bit,
@byPassAccrual bit,
@xmlSchedule xml,
@transactionID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @origSaleOwnedByOrgID int, @origSaleAssignedToMemberID int, @origCreditGLAccountID int, @ARGLAccountID int, 
		@AdjSaleTransactionID int, @taxTransactionID int, @AdjTaxTransactionID int, @invoiceProfileID int, 
		@contentVersionID int, @ditTransactionID int, @ditTransactionID2 int, @AdjDeferredGLAccountID int, 
		@minSchedRow int, @minTaxrow int, @taxGLAID int, @taxDeferredGLAccountID int, @TaxAdjDeferredGLAccountID int, 
		@TaxminSchedRow int, @taxditTransactionID int, @taxditTransactionID2 int, @PITTaxTrans int, @PITTaxTrans2 int,
		@amtLeftToDeallocate decimal(18,2), @AllocAmt decimal(18,2), @DeallocateAmtNeg decimal(18,2), @invoiceNumber varchar(18), 
		@origSaleDetail varchar(500), @taxdetail varchar(500), @TaxNeeded bit, @rowAmt decimal(18,2), 
		@taxAmt decimal(18,2), @taxSum decimal(18,2), @taxDiff decimal(18,2), @taxrowAmt decimal(18,2), 
		@rowDt datetime, @taxrowDt datetime, @taxXMLSchedule xml, @saleTransactionDate datetime, @tr_AdjustTrans int, 
		@maxTransactionDate datetime, @tr_SalesTaxTrans int, @tr_PITTaxTrans int, @ts_active int, @bypassInBounds bit,
		@origSaleItemID int, @origSaleItemType varchar(30), @origInvoiceID int, @merchantProfileID int, @payProfileID int, 
		@payProcessFee bit, @processFeePercent decimal(5,2);
	declare @tblDeferredSchedule TABLE (rowID int, amt decimal(18,2), dt datetime, pct decimal(18,2), ditTID int, dit2TID int);
	declare @tblTaxDeferredSchedule TABLE (rowID int, amt decimal(18,2), dt datetime, pct decimal(18,2), ditTID int, dit2TID int);

	SET @transactionID = null;
	set @tr_AdjustTrans = dbo.fn_tr_getRelationshipTypeID('AdjustTrans');
	set @tr_PITTaxTrans = dbo.fn_tr_getRelationshipTypeID('PITTaxTrans');
	set @ts_active = dbo.fn_tr_getStatusID('Active');

	-- get data from sale transaction
	select @origSaleOwnedByOrgID=ownedByOrgID, @origSaleAssignedToMemberID=assignedToMemberID, 
		@origSaleDetail=detail, @origCreditGLAccountID=creditGLAccountID, @saleTransactionDate=transactionDate
		from dbo.tr_transactions
		where transactionID = @saleTransactionID;

	-- dont assume memberid is the active one. get the active one.
	select @origSaleAssignedToMemberID = activeMemberID
		from dbo.ams_members
		where memberID = @origSaleAssignedToMemberID;
	select @recordedByMemberID = activeMemberID
		from dbo.ams_members
		where memberID = @recordedByMemberID;

	-- sale item id and type
	SELECT @origSaleItemID = itemID, @origSaleItemType = itemType
	FROM dbo.tr_applications
	WHERE transactionID = @saleTransactionID
	AND orgID = @origSaleOwnedByOrgID;

	SELECT TOP 1 @origInvoiceID = invoiceID
	FROM dbo.tr_invoiceTransactions
	WHERE orgID = @origSaleOwnedByOrgID
	AND transactionID = @saleTransactionID;

	EXEC dbo.tr_getGLAccountByGLCode @orgID=@origSaleOwnedByOrgID, @GLCode='ACCOUNTSRECEIVABLE', @GLAccountID=@ARGLAccountID OUTPUT;
	IF @ARGLAccountID is null
		RAISERROR('ARGLAccountID is null', 16, 1);

	-- ensure amount is abs
	set @amount = cast(abs(@amount) as decimal(18,2));
	set @taxAmount = cast(abs(@taxAmount) as decimal(18,2));

	-- if @autoAdjustTransactionDate = 1 then check transactionDate. cant be earlier than max transaction date in sale/adj tree.
	IF @autoAdjustTransactionDate = 1 BEGIN
		select @maxTransactionDate = max(transactionDate)
		from (
			select @saleTransactionDate as transactionDate
				union
			select t.transactionDate
			from dbo.tr_transactions as t
			inner join dbo.tr_relationships as trAdj on trAdj.orgID = @origSaleOwnedByOrgID
				and trAdj.typeID = @tr_AdjustTrans 
				and trAdj.transactionID = t.transactionID
				and trAdj.appliedToTransactionID = @saleTransactionID
			where t.ownedByOrgID = @origSaleOwnedByOrgID
		) as tmp;

		IF @transactionDate <= @maxTransactionDate
			SET @transactionDate = DATEADD(ss,1,@maxTransactionDate);
	END
	
	BEGIN TRAN;
		-- if invoiceID is null, not an open/pending invoice, or inv profile doesnt match revenue GL, assume need to create a new one.
		IF @invoiceID is null 
			OR NOT EXISTS (select invoiceID from dbo.tr_invoices where invoiceID = @invoiceID and statusID in (1,2))
			OR ((select dbo.fn_tr_doesInvoiceProfileSupportRevenueGL(@invoiceID,@origCreditGLAccountID)) = 0)
		BEGIN
			select @invoiceProfileID=invoiceProfileID from dbo.tr_GLAccounts where GLAccountID = @origCreditGLAccountID;
			IF @invoiceProfileID is null 
				RAISERROR('invoiceProfileID is null', 16, 1);

			EXEC dbo.tr_createInvoice @invoiceProfileID=@invoiceProfileID, @enteredByMemberID=@recordedByMemberID, 
				@assignedToMemberID=@origSaleAssignedToMemberID, @dateBilled=@transactionDate, @dateDue=@transactionDate, 
				@invoiceID=@invoiceID OUTPUT, @invoiceNumber=@invoiceNumber OUTPUT;

			IF @origInvoiceID IS NOT NULL BEGIN
				SELECT @merchantProfileID = MPProfileID, @payProfileID = payProfileID, @payProcessFee = payProcessFee, 
					@processFeePercent = processFeePercent
				FROM dbo.tr_invoices
				WHERE invoiceID = @origInvoiceID;

				UPDATE dbo.tr_invoices
				SET payProfileID = @payProfileID,
					MPProfileID = @merchantProfileID,
					payProcessFee = @payProcessFee,
					processFeePercent = @processFeePercent
				WHERE invoiceID = @invoiceID;
			END
		END
	
		-- insert adj into transactions
		-- ensure amount is abs
		INSERT INTO dbo.tr_transactions (ownedByOrgID, recordedOnSiteID, statusID, detail, parentTransactionID, 
			amount, dateRecorded, transactionDate, assignedToMemberID, recordedByMemberID, statsSessionID, 
			typeID, debitGLAccountID, creditGLAccountID)
		VALUES (@origSaleOwnedByOrgID, @recordedOnSiteID, @ts_active, @origSaleDetail, null, 
			@amount, getdate(), @transactionDate, @origSaleAssignedToMemberID, @recordedByMemberID, @statsSessionID, 
			dbo.fn_tr_getTypeID('Adjustment'), @ARGLAccountID, @origCreditGLAccountID);
		select @AdjSaleTransactionID = SCOPE_IDENTITY();
		set @transactionID = @AdjSaleTransactionID;

		-- insert adj into relationships
		INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID, orgID)
		VALUES (@tr_AdjustTrans, @AdjSaleTransactionID, @saleTransactionID, @origSaleOwnedByOrgID);

		-- put adj on invoice
		SET @contentVersionID = null;
		SELECT @contentVersionID = max(cv.contentVersionID)
			FROM dbo.tr_glAccounts as gl
			INNER JOIN dbo.cms_content as c on c.contentID = gl.invoiceContentID
			INNER JOIN dbo.cms_siteResources sr	on sr.siteResourceID = c.siteResourceID 
			INNER JOIN dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID and srs.siteResourceStatusDesc = 'Active'
			INNER JOIN dbo.cms_contentLanguages as cl ON cl.contentID = c.contentID AND cl.languageID = 1
			INNER JOIN dbo.cms_contentVersions as cv on cv.contentLanguageID = cl.contentLanguageID
			WHERE gl.orgID = @origSaleOwnedByOrgID
			and gl.GLAccountID = @origCreditGLAccountID
			AND cv.isActive = 1
			AND len(cv.rawContent) > 0;
		INSERT INTO dbo.tr_invoiceTransactions (transactionID, invoiceID, cache_invoiceAmountAfterAdjustment, cache_activePaymentAllocatedAmount, cache_pendingPaymentAllocatedAmount, messageContentVersionID, orgID)
		VALUES (@AdjSaleTransactionID, @invoiceID, @amount, 0, 0, @contentVersionID, @origSaleOwnedByOrgID);

		-- update cache
		UPDATE dbo.tr_transactionSales
		SET cache_amountAfterAdjustment = cache_amountAfterAdjustment + @amount
		WHERE orgID = @origSaleOwnedByOrgID
		and transactionID = @saleTransactionID;

		IF @byPassTax = 0 BEGIN
			/* ******************* */
			/* DETERMINE SALES TAX */
			/* ******************* */
			-- Just get information - we'll record it after deferred is recorded.
			-- we need to do this before the deferred entries are written because the deferred entries adjust the tr_transactionSales amount used by tax determination.
			declare @salesTaxProfileID int, @salesTaxProviderName varchar(20), @TJTaxCode varchar(15), @taxJarTaxRate decimal(6,5);
			DECLARE @tblTax TABLE (row int, taxAuthorityID int, taxGLAccountID int, taxDeferredGLAccountID int, authorityName varchar(40), taxRate decimal(6,5), taxAmount decimal(18,2));
			set @TaxNeeded = 0;

			select @salesTaxProfileID=stp.profileID, @salesTaxProviderName=stpr.providerName, @TJTaxCode=tj.taxCode
			from dbo.tr_glAccounts as gl
			inner join dbo.tr_salesTaxProfiles as stp on stp.profileID = gl.salesTaxProfileID
			inner join dbo.tr_salesTaxProviders as stpr on stpr.providerID = stp.providerID
			left outer join dbo.tr_salesTaxCategories_TaxJar as tj on tj.categoryID = gl.salesTaxTaxJarCategoryID
			where gl.orgID = @origSaleOwnedByOrgID
			and gl.glAccountID = @origCreditGLAccountID
			and stp.status = 'A';

			-- MC sales tax
			IF @salesTaxProviderName = 'MemberCentral' BEGIN
				insert into @tblTax	(row, taxAuthorityID, taxGLAccountID, taxDeferredGLAccountID, authorityName, taxRate, taxAmount)
				select row, taxAuthorityID, taxGLAccountID, deferredGLAccountID, authorityName, taxRate, taxAmount
				from dbo.fn_tr_getTaxForAdjustment(@origSaleOwnedByOrgID,@AdjSaleTransactionID)
				where taxAmount > 0;

				IF @@rowcount > 0				
					set @TaxNeeded = 1;
			END

			-- TaxJar sales tax
			IF @salesTaxProviderName = 'TaxJar' BEGIN

				-- if not tied to tax exempt category, check taxAmount
				IF @TJTaxCode <> '99999' AND @taxAmount is null
					RAISERROR('TaxJar Tax Amount required', 16, 1);

				IF @TaxAmount is not null and @taxAmount > 0 BEGIN
					set @taxJarTaxRate = cast((cast(@taxAmount as decimal(18,5)) / cast(@amount as decimal(18,5))) as decimal(6,5));

					insert into @tblTax	(row, taxAuthorityID, taxGLAccountID, taxDeferredGLAccountID, authorityName, taxRate, taxAmount)
					select 1, ta.taxAuthorityID, ta.GLAccountID, null, ta.AuthorityName, @taxJarTaxRate, @taxAmount
					from dbo.tr_taxAuthorities as ta
					where ta.salesTaxProfileID = @salesTaxProfileID
					and ta.status = 'A';

					IF @@rowcount > 0				
						set @TaxNeeded = 1;
				END

			END
		END

		IF @byPassAccrual = 0 BEGIN
			-- prepare deferred schedule
			insert into @tblDeferredSchedule (rowID, amt, dt, pct, ditTID, dit2TID)
			select rowID, amt, dt, pct, ditTID, dit2TID
			from dbo.fn_tr_prepareDeferredScheduleTable(@xmlSchedule, @amount, @transactionDate);

			-- record deferred schedule for sale if necessary
			select @AdjDeferredGLAccountID = dbo.fn_tr_getDeferredGLAccountID(@origCreditGLAccountID);
			IF @AdjDeferredGLAccountID is not null BEGIN
				select @minSchedRow = min(rowID) from @tblDeferredSchedule;
				WHILE @minSchedRow is not null BEGIN
					select @rowAmt=amt, @rowDt=dt 
					from @tblDeferredSchedule 
					where rowID = @minSchedRow;

					IF @rowDt < getdate()
						SET @bypassInBounds = 1;
					ELSE
						SET @bypassInBounds = 0;
					
					-- put all scheduled rows here for auditing, even if they are immediately recognized
					EXEC dbo.tr_createTransaction_dit @recordedOnSiteID=@recordedOnSiteID, 
						@recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID, 
						@amount=@rowAmt, @transactionDate=@transactionDate, @recognitionDate=@rowDt, 
						@debitGLAccountID=@origCreditGLAccountID, @creditGLAccountID=@AdjDeferredGLAccountID, 
						@saleTransactionID=@AdjSaleTransactionID, @DITTransactionID=null, @batchAsRecogJob=0, 
						@bypassInBounds=@bypassInBounds, @transactionID=@ditTransactionID OUTPUT;
					update @tblDeferredSchedule set ditTID = @ditTransactionID where rowID = @minSchedRow;

					-- if recognition date is today or in past, then move it out immediately back to revenue
					IF @rowDt < getdate() BEGIN
						set @rowAmt = @rowAmt * -1;
						EXEC dbo.tr_createTransaction_dit @recordedOnSiteID=@recordedOnSiteID, 
							@recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID, 
							@amount=@rowAmt, @transactionDate=@transactionDate, @recognitionDate=null, 
							@debitGLAccountID=@AdjDeferredGLAccountID, @creditGLAccountID=@origCreditGLAccountID, 
							@saleTransactionID=@AdjSaleTransactionID, @DITTransactionID=@ditTransactionID, 
							@batchAsRecogJob=0, @bypassInBounds=@bypassInBounds, @transactionID=@ditTransactionID2 OUTPUT;
						update @tblDeferredSchedule set dit2TID = @ditTransactionID2 where rowID = @minSchedRow;
					END

					select @minSchedRow = min(rowID) from @tblDeferredSchedule where rowID > @minSchedRow;
				END
			END
		END

		-- record sales tax
		IF @byPassTax = 0 and @TaxNeeded = 1 BEGIN
			set @tr_SalesTaxTrans = dbo.fn_tr_getRelationshipTypeID('SalesTaxTrans');

			select @minTaxrow = min(row) from @tblTax;
			WHILE @minTaxrow is not null BEGIN
				set @taxXMLSchedule = null;
				
				select @taxAmt=taxAmount, @taxGLAID=taxGLAccountID, @taxDeferredGLAccountID = taxDeferredGLAccountID, 
					@taxdetail = 'Sales Tax: ' + authorityName + ' @ ' + FORMAT(taxRate*100, '0.0####') + '%'
				from @tblTax 
				where row=@minTaxrow;

				-- if deferring tax, calculate percentage of SAME schedule as adj and put remainder on first row
				IF @taxDeferredGLAccountID is not null BEGIN
					update @tblDeferredSchedule set amt = @taxAmt * pct;
					select @taxSum = sum(amt) from @tblDeferredSchedule;
					set @taxDiff = @taxAmt - @taxSum;
					if @taxDiff <> 0
						update @tblDeferredSchedule set amt = amt + @taxDiff where rowID = 1;

					SELECT @taxXMLSchedule = (
						select amt, convert(varchar(10),dt,101) as dt, ditTID as dittid, dit2TID as dit2tid
						from @tblDeferredSchedule as row
						for XML AUTO, ROOT('rows'), TYPE
						);
				END

				-- is there already a tax transaction using this acct code for this sale? if so, adjust it
				SET @taxTransactionID = null;
				SELECT @taxTransactionID = tTax.transactionID
					FROM dbo.tr_transactions as tTax
					INNER JOIN dbo.tr_relationships AS r ON r.orgID = @origSaleOwnedByOrgID
						and r.typeID = @tr_SalesTaxTrans 
						and r.transactionID = tTax.transactionID 
						AND r.appliedToTransactionID = @saleTransactionID
					WHERE tTax.ownedByOrgID = @origSaleOwnedByOrgID
					and tTax.typeID = 7
					AND tTax.statusID = 1
					AND tTax.creditGLAccountID = @taxGLAID;
				IF @taxTransactionID is not null BEGIN

					-- insert adj into transactions
					INSERT INTO dbo.tr_transactions (ownedByOrgID, recordedOnSiteID, statusID, detail, parentTransactionID, 
						amount, dateRecorded, transactionDate, assignedToMemberID, recordedByMemberID, statsSessionID, 
						typeID, debitGLAccountID, creditGLAccountID)
					VALUES (@origSaleOwnedByOrgID, @recordedOnSiteID, @ts_active, @taxdetail, null, 
						@taxAmt, getdate(), @transactionDate, @origSaleAssignedToMemberID, @recordedByMemberID, @statsSessionID, 
						dbo.fn_tr_getTypeID('Adjustment'), @ARGLAccountID, @taxGLAID);
					select @AdjTaxTransactionID = SCOPE_IDENTITY();

					-- insert adj into relationships
					-- tie tax to the sale adjustment
					INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID, orgID)
					VALUES (dbo.fn_tr_getRelationshipTypeID('AdjustTrans'), @AdjTaxTransactionID, @taxTransactionID, @origSaleOwnedByOrgID);
					
					INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID, orgID)
					VALUES (@tr_PITTaxTrans, @AdjTaxTransactionID, @AdjSaleTransactionID, @origSaleOwnedByOrgID);

					-- put adj on invoice
					INSERT INTO dbo.tr_invoiceTransactions (transactionID, invoiceID, cache_invoiceAmountAfterAdjustment, cache_activePaymentAllocatedAmount, cache_pendingPaymentAllocatedAmount, orgID)
					VALUES (@AdjTaxTransactionID, @invoiceID, @taxAmt, 0, 0, @origSaleOwnedByOrgID);

					-- update cache
					UPDATE dbo.tr_transactionSales
					SET cache_amountAfterAdjustment = cache_amountAfterAdjustment + @taxAmt
					WHERE orgID = @origSaleOwnedByOrgID
					and transactionID = @taxTransactionID;

					-- prepare deferred schedule
					insert into @tblTaxDeferredSchedule (rowID, amt, dt, pct, ditTID, dit2TID)
					select rowID, amt, dt, pct, ditTID, dit2TID
					from dbo.fn_tr_prepareDeferredScheduleTable(@taxXMLSchedule, @taxAmt, @transactionDate);

					-- record deferred schedule for tax if necessary
					select @TaxAdjDeferredGLAccountID = dbo.fn_tr_getDeferredGLAccountID(@taxGLAID);
					IF @TaxAdjDeferredGLAccountID is not null BEGIN
						select @TaxminSchedRow = min(rowID) from @tblTaxDeferredSchedule;
						WHILE @TaxminSchedRow is not null BEGIN
							select @taxrowAmt=null, @taxrowDt=null, @PITTaxTrans=null, @PITTaxTrans2=null;
							select @taxrowAmt=amt, @taxrowDt=dt, @PITTaxTrans=ditTID, @PITTaxTrans2=dit2TID
							from @tblTaxDeferredSchedule 
							where rowID = @TaxminSchedRow;

							IF @taxrowDt < getdate()
								SET @bypassInBounds = 1;
							ELSE
								SET @bypassInBounds = 0;
							
							-- put all scheduled rows here for auditing, even if they are immediately recognized
							EXEC dbo.tr_createTransaction_dit @recordedOnSiteID=@recordedOnSiteID, 
								@recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID, 
								@amount=@taxrowAmt, @transactionDate=@transactionDate, @recognitionDate=@taxrowDt, 
								@debitGLAccountID=@taxGLAID, @creditGLAccountID=@TaxAdjDeferredGLAccountID, 
								@saleTransactionID=@AdjTaxTransactionID, @DITTransactionID=null, @batchAsRecogJob=0, 
								@bypassInBounds=@bypassInBounds, @transactionID=@taxditTransactionID OUTPUT;

							IF @PITTaxTrans is not null 
								INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID, orgID)
								VALUES (@tr_PITTaxTrans, @taxditTransactionID, @PITTaxTrans, @origSaleOwnedByOrgID);

							-- if recognition date is today or in past, then move it out immediately back to revenue
							IF @taxrowDt < getdate() BEGIN
								set @taxrowAmt = @taxrowAmt * -1;
								EXEC dbo.tr_createTransaction_dit @recordedOnSiteID=@recordedOnSiteID, 
									@recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID, 
									@amount=@taxrowAmt, @transactionDate=@transactionDate, @recognitionDate=null, 
									@debitGLAccountID=@TaxAdjDeferredGLAccountID, @creditGLAccountID=@taxGLAID, 
									@saleTransactionID=@AdjTaxTransactionID, @DITTransactionID=@taxditTransactionID, 
									@batchAsRecogJob=0, @bypassInBounds=@bypassInBounds, @transactionID=@taxditTransactionID2 OUTPUT;

								IF @PITTaxTrans2 is not null 
									INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID, orgID)
									VALUES (@tr_PITTaxTrans, @taxditTransactionID2, @PITTaxTrans2, @origSaleOwnedByOrgID);
							END

							select @TaxminSchedRow = min(rowID) from @tblTaxDeferredSchedule where rowID > @TaxminSchedRow;
						END
					END

				END 

				-- else, create a new sales tax transaction.
				ELSE BEGIN

					EXEC dbo.tr_createTransaction_salesTax @ownedByOrgID=@origSaleOwnedByOrgID, 
						@recordedOnSiteID=@recordedOnSiteID, @recordedByMemberID=@recordedByMemberID, 
						@statsSessionID=@statsSessionID, @status='Active', @detail=@taxdetail, 
						@amount=@taxAmt, @transactionDate=@transactionDate, 
						@creditGLAccountID=@taxGLAID, @saleTransactionID=@saleTransactionID, 
						@invoiceID=@invoiceID, @xmlSchedule=@taxXMLSchedule, @transactionID=@taxTransactionID OUTPUT;

					-- tie tax to the sale adjustment
					INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID, orgID)
					VALUES (@tr_PITTaxTrans, @taxTransactionID, @AdjSaleTransactionID, @origSaleOwnedByOrgID);

				END

				select @minTaxrow = min(row) from @tblTax where row > @minTaxrow;
			END
		END

		IF @origSaleItemID IS NOT NULL
			EXEC dbo.tr_createInvoiceItem @orgID=@origSaleOwnedByOrgID, @siteID=@recordedOnSiteID, @invoiceID=@invoiceID, @itemID=@origSaleItemID, @itemType=@origSaleItemType;

		-- add adjustment to limit checking table
		INSERT INTO platformQueue.dbo.queue_transactionLimitFlagging (transactionID, orgID, siteID)
		VALUES (@transactionID, @origSaleOwnedByOrgID, @recordedOnSiteID);

	COMMIT TRAN;
	
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
