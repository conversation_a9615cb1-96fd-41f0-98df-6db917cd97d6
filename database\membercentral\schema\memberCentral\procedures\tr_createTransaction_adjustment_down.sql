ALTER PROC dbo.tr_createTransaction_adjustment_down
@recordedOnSiteID int,
@recordedByMemberID int,
@statsSessionID int,
@amount decimal(18,2),
@transactionDate datetime,
@autoAdjustTransactionDate bit,
@saleTransactionID int,
@invoiceID int,
@transactionID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @origSaleOwnedByOrgID int, @origSaleAssignedToMemberID int, @origCreditGLAccountID int, 
		@ARGLAccountID int, @AdjSaleTransactionID int, @AdjTaxTransactionID int, @invoiceProfileID int, 
		@contentVersionID int, @ditTransactionID int, @ditTransactionID2 int, @SaleAndAdjustmentsMinAutoID int, 
		@SaleAndAdjustmentsTID int, @SaleAndAdjustmentsTypeID int, @AdjToMakeAutoID int, @AdjToMakeTID int, 
		@AdjToMakeGLAID int, @a_autoid int, @a_paymentTID int, @it_invoiceID int, @AdjToMakeSaleAdjTID int, 
		@ditspreadAutoID int, @ditSpreadDITTID int, @ditSpreadDebitGL int, @ditSpreadCreditGL int, 
		@newdittransactionID int, @SaleAndAdjustmentsInvID int, @respreadFirstAutoID int, @saleTransactionDate datetime, 
		@respreadSaleAdjTID int, @respreadAutoID int, @respreadDebitGL int, @respreadCreditGL int, 
		@amtLeftToDeallocate decimal(18,2), @AllocAmt decimal(18,2), @amtLeftToAdjust decimal(18,2), @SaleAndAdjustmentsAmt numeric(15,6), 
		@amountToAdjust decimal(15,6), @AdjToMakeAmt decimal(18,2), @AdjToMakeUnallocatedAmt decimal(18,2), @amountToAllocate decimal(18,2), 
		@a_allocAmount decimal(18,2), @AdjToMakeDITSum decimal(15,6), @amtDueNoPendingOnInvoice decimal(18,2), 
		@ditSpreadDITCacheAmount decimal(18,2), @amtToRespread decimal(18,2), @respreadSum decimal(18,2), @respreadDiff decimal(18,2),
		@invoiceNumber varchar(18), @it_invstatusID int, @it_invstatus varchar(10), @origSaleDetail varchar(500), 
		@AdjToMakeDetail varchar(500), @AdjToMakeIsSale bit, @ditSpreadRecogDt datetime, @trashID int,
		@maxTransactionDate datetime, @tr_AdjustTrans int, @tr_PITTaxTrans int, @tr_DITSaleTrans int,
		@tr_AdjustInvTrans int, @tr_AdjustTSaleTrans int, @ts_active int, @tsaaRowNum int, @bypassInBounds bit,
		@origSaleItemID int, @origSaleItemType varchar(30);
	declare @tblSaleAndAdjustments TABLE (autoid int PRIMARY KEY, transactionID int, typeID int, amount decimal(18,2), invoiceID int);
	declare @tblAdjToMake TABLE (autoid int IDENTITY(1,1), isSale bit, saleTransactionID int, saleAdjTID int, 
		debitGLAID int, detail varchar(500), amountToAdjust decimal(18,2), adjTransactionID int, SaleAdjInvoiceID int);
	declare @tblAdjToMakeFinal TABLE (autoid int IDENTITY(1,1), isSale bit, saleTransactionID int, debitGLAID int,
		detail varchar(500), amountToAdjust decimal(18,2), adjTransactionID int);
	declare @tblAllocations TABLE (autoid int IDENTITY(1,1) PRIMARY KEY, transactionID int, PITTaxTID int, allocAmount decimal(18,2),
		allocDate datetime, detail varchar(500), transactionDate datetime);
	declare @tblDITSpread TABLE (autoID int IDENTITY(1,1) PRIMARY KEY, saleAdjTID int, ditTransactionID int,
		ditAmount decimal(18,2), ditCacheAmount decimal(18,2), recogDt datetime, pct numeric(18,2), ditdebitGLAID int,
		ditcreditGLAID int, respreadAmount decimal(18,2));
	declare @tblTransOutOfOrder TABLE (transactionID int);

	set @transactionID = null;
	set @tr_AdjustTrans = dbo.fn_tr_getRelationshipTypeID('AdjustTrans');
	set @tr_PITTaxTrans = dbo.fn_tr_getRelationshipTypeID('PITTaxTrans');
	set @tr_DITSaleTrans = dbo.fn_tr_getRelationshipTypeID('DITSaleTrans');
	set @tr_AdjustInvTrans = dbo.fn_tr_getRelationshipTypeID('AdjustInvTrans');
	set @tr_AdjustTSaleTrans = dbo.fn_tr_getRelationshipTypeID('AdjustTSaleTrans');
	set @ts_active = dbo.fn_tr_getStatusID('Active');

	-- get data from sale transaction
	select @origSaleOwnedByOrgID=ownedByOrgID, @origSaleAssignedToMemberID=assignedToMemberID, 
		@origSaleDetail=detail, @origCreditGLAccountID=creditGLAccountID, @saleTransactionDate=transactionDate
	from dbo.tr_transactions
	where transactionID = @saleTransactionID;

	-- dont assume memberid is the active one. get the active one.
	select @origSaleAssignedToMemberID = activeMemberID
		from dbo.ams_members
		where memberID = @origSaleAssignedToMemberID;
	select @recordedByMemberID = activeMemberID
		from dbo.ams_members
		where memberID = @recordedByMemberID;

	-- sale item id and type
	SELECT @origSaleItemID = itemID, @origSaleItemType = itemType
	FROM dbo.tr_applications
	WHERE transactionID = @saleTransactionID
	AND orgID = @origSaleOwnedByOrgID;

	EXEC dbo.tr_getGLAccountByGLCode @orgID=@origSaleOwnedByOrgID, @GLCode='ACCOUNTSRECEIVABLE', @GLAccountID=@ARGLAccountID OUTPUT;
	IF @ARGLAccountID is null
		RAISERROR('ARGLAccountID is null', 16, 1);

	-- if @autoAdjustTransactionDate = 1 then check transactionDate. cant be earlier than max transaction date in sale/adj tree.
	IF @autoAdjustTransactionDate = 1 BEGIN
		select @maxTransactionDate = max(transactionDate)
		from (
			select @saleTransactionDate as transactionDate
				union
			select t.transactionDate
			from dbo.tr_transactions as t
			inner join dbo.tr_relationships as trAdj on trAdj.orgID = @origSaleOwnedByOrgID 
				and trAdj.typeID = @tr_AdjustTrans 
				and trAdj.transactionID = t.transactionID
				and trAdj.appliedToTransactionID = @saleTransactionID
			where t.ownedByOrgID = @origSaleOwnedByOrgID
		) as tmp;

		IF @transactionDate <= @maxTransactionDate
			SET @transactionDate = DATEADD(ss,1,@maxTransactionDate);
	END

	-- get all active adjustments to sale in reverse transaction date order.
	-- consider the non-written off amounts only and remove any that are 0
	insert into @tblSaleAndAdjustments (autoID, transactionID, typeID, amount, invoiceID)
	select ROW_NUMBER() OVER (ORDER BY tAdj.transactionDate desc, tAdj.transactionID desc) as autoID,
		tAdj.transactionID, tAdj.typeID, it.cache_invoiceAmountAfterAdjustment-wo.writeOffAmount, it.invoiceID
	from dbo.tr_transactions as tAdj
	inner join dbo.tr_relationships as tr on tr.orgID = @origSaleOwnedByOrgID 
		and tr.typeID = @tr_AdjustTrans 
		and tr.transactionID = tAdj.transactionID 
		and tr.appliedToTransactionID = @saleTransactionID
	inner join dbo.tr_invoiceTransactions as it on it.orgID = @origSaleOwnedByOrgID and it.transactionID = tAdj.transactionID
	cross apply dbo.fn_tr_getWriteOffAmountofSaleOrAdj(@origSaleOwnedByOrgID,tAdj.transactionID) as wo
	where tAdj.ownedByOrgID = @origSaleOwnedByOrgID
	and tAdj.statusID = 1
	and it.cache_invoiceAmountAfterAdjustment-wo.writeOffAmount > 0;

	-- get how many we just inserted. the next autoID will be that count + 1
	select @tsaaRowNum = @@ROWCOUNT;
		
	-- add in the original sale as last entry
	insert into @tblSaleAndAdjustments (autoID, transactionID, typeID, amount, invoiceID)
	select @tsaaRowNum+1 as autoID, tSale.transactionID, tSale.typeID, it.cache_invoiceAmountAfterAdjustment-wo.writeOffAmount, it.invoiceID
	from dbo.tr_transactions as tSale
	inner join dbo.tr_invoiceTransactions as it on it.orgID = @origSaleOwnedByOrgID and it.transactionID = tSale.transactionID
	cross apply dbo.fn_tr_getWriteOffAmountofSaleOrAdj(@origSaleOwnedByOrgID,tSale.transactionID) as wo
	where tSale.ownedByOrgID = @origSaleOwnedByOrgID
	and tSale.transactionID = @saleTransactionID
	and it.cache_invoiceAmountAfterAdjustment-wo.writeOffAmount > 0;

	-- loop over @tblSaleAndAdjustments until we get adjustment amount. grab PIT tax as well.		
	set @amtLeftToAdjust = abs(@amount);
	select @SaleAndAdjustmentsMinAutoID = min(autoid) from @tblSaleAndAdjustments;
	while @SaleAndAdjustmentsMinAutoID is not null BEGIN
		select @SaleAndAdjustmentsTID=transactionID, @SaleAndAdjustmentsTypeID=typeID, @SaleAndAdjustmentsAmt=amount, @SaleAndAdjustmentsInvID=invoiceID
		from @tblSaleAndAdjustments 
		where autoID = @SaleAndAdjustmentsMinAutoID;

		-- if amt left can be adjusted in full from this adjustment, take full amt. else take what we can.
		if @SaleAndAdjustmentsAmt < @amtLeftToAdjust
			set @amountToAdjust = @SaleAndAdjustmentsAmt;
		ELSE
			set @amountToAdjust = @amtLeftToAdjust;

		-- add to adj to make. adj debit acct is orig credit acct
		insert into @tblAdjToMake (saleTransactionID, saleAdjTID, isSale, debitGLAID, detail, amountToAdjust, SaleAdjInvoiceID)
		values (@saleTransactionID, @SaleAndAdjustmentsTID, 1, @origCreditGLAccountID, @origSaleDetail, @amountToAdjust*-1, @SaleAndAdjustmentsInvID);
				
		-- and all its PIT taxes (adj to sales tax and sales tax). adj debit acct is orig credit acct
		-- 1. adjust to sales tax tied to adjust to sale (could be pos or neg so find out based on AR)
		-- 2+3. sales tax tied to sale or adjust to sale (only happens on positive adjustments so just take adj amount)
		insert into @tblAdjToMake (saleTransactionID, saleAdjTID, isSale, debitGLAID, detail, amountToAdjust, SaleAdjInvoiceID)
		SELECT tTax.transactionID, tAdj.transactionID, 0, tTax.creditGLAccountID, tTax.detail, 
			case 
			when tAdj.debitGLAccountID = @ARGLAccountID then ((((it.cache_invoiceAmountAfterAdjustment-wo.writeOffAmount)/@SaleAndAdjustmentsAmt)*@amountToAdjust)*-1)
			else (((it.cache_invoiceAmountAfterAdjustment-wo.writeOffAmount)/@SaleAndAdjustmentsAmt)*@amountToAdjust)
			end as amountToAdjust, it.invoiceID
		FROM dbo.tr_transactions AS tAdj 
		INNER JOIN dbo.tr_relationships AS tr ON tr.orgID = @origSaleOwnedByOrgID
			and tr.typeID = @tr_PITTaxTrans 
			and tAdj.transactionID = tr.transactionID 
			and tr.appliedToTransactionID = @SaleAndAdjustmentsTID
		INNER JOIN dbo.tr_relationships AS tr2 ON tr2.orgID = @origSaleOwnedByOrgID and tr2.typeID = @tr_AdjustTrans and tAdj.transactionID = tr2.transactionID 
		INNER JOIN dbo.tr_transactions AS tTax ON tTax.ownedByOrgID = @origSaleOwnedByOrgID and tr2.appliedToTransactionID = tTax.transactionID
		INNER JOIN dbo.tr_invoiceTransactions as it on it.orgID = @origSaleOwnedByOrgID and it.transactionID = tAdj.transactionID
		cross apply dbo.fn_tr_getWriteOffAmountofSaleOrAdj(@origSaleOwnedByOrgID,tAdj.transactionID) as wo
		WHERE tAdj.ownedByOrgID = @origSaleOwnedByOrgID
		and @SaleAndAdjustmentsTypeID = 3
		AND tAdj.typeID = 3
		AND tAdj.statusID = 1
		AND tTax.typeID = 7
		AND tTax.statusID = 1
			union all
		SELECT tTax.transactionID, tTax.transactionID, 0, tTax.creditGLAccountID, tTax.detail, 
			((((it.cache_invoiceAmountAfterAdjustment-wo.writeOffAmount)/@SaleAndAdjustmentsAmt)*@amountToAdjust)*-1) as amountToAdjust, it.invoiceID
		FROM dbo.tr_transactions AS tTax 
		INNER JOIN dbo.tr_relationships AS tr ON tr.orgID = @origSaleOwnedByOrgID
			and tr.typeID = @tr_PITTaxTrans 
			and tTax.transactionID = tr.transactionID
			and tr.appliedToTransactionID = @SaleAndAdjustmentsTID
		INNER JOIN dbo.tr_invoiceTransactions as it on it.orgID = @origSaleOwnedByOrgID and it.transactionID = tTax.transactionID
		cross apply dbo.fn_tr_getWriteOffAmountofSaleOrAdj(@origSaleOwnedByOrgID,tTax.transactionID) as wo
		WHERE tTax.ownedByOrgID = @origSaleOwnedByOrgID
		and tTax.typeID = 7 
		AND tTax.statusID = 1
		AND @SaleAndAdjustmentsTypeID in (1,3);

		set @amtLeftToAdjust = @amtLeftToAdjust - @amountToAdjust;
		IF @amtLeftToAdjust <= 0
			BREAK;

		select @SaleAndAdjustmentsMinAutoID = min(autoid) from @tblSaleAndAdjustments where autoid > @SaleAndAdjustmentsMinAutoID;
	END


	BEGIN TRAN;
		-- if invoiceID is null, not an open/pending invoice, or inv profile doesnt match revenue GL, assume need to create a new one.
		select @trashID = invoiceID from dbo.tr_invoices where invoiceID = @invoiceID and statusID in (1,2);
		IF @invoiceID is null OR @trashID is null OR ((select dbo.fn_tr_doesInvoiceProfileSupportRevenueGL(@invoiceID,@origCreditGLAccountID)) = 0) BEGIN
			select @invoiceProfileID=invoiceProfileID from dbo.tr_GLAccounts where GLAccountID = @origCreditGLAccountID;
			IF @invoiceProfileID is null 
				RAISERROR('invoiceProfileID is null', 16, 1);

			EXEC dbo.tr_createInvoice @invoiceProfileID=@invoiceProfileID, @enteredByMemberID=@recordedByMemberID, 
				@assignedToMemberID=@origSaleAssignedToMemberID, @dateBilled=@transactionDate, @dateDue=@transactionDate, 
				@invoiceID=@invoiceID OUTPUT, @invoiceNumber=@invoiceNumber OUTPUT;
		END
	
		-- loop over the @tblAdjToMake to ensure everything is recognized (not deferred) and enough is unallocated for adjustment
		select @AdjToMakeAutoID = min(autoid) from @tblAdjToMake;
		while @AdjToMakeAutoID is not null BEGIN
			select @AdjToMakeSaleAdjTID=saleAdjTID, @AdjToMakeAmt=amountToAdjust
			from @tblAdjToMake 
			where autoid = @AdjToMakeAutoID;

			-- Get all active DIT linked to tr_transactionDIT with dit amount, tr_transactionSales amount, and dit recog date.			
			insert into @tblDITSpread (saleAdjTID, ditTransactionID, ditAmount, ditCacheAmount, recogDt, ditdebitGLAID, ditcreditGLAID)
			select r.appliedToTransactionID, t.transactionID, t.amount, ts.cache_amountAfterAdjustment, dit.recognitionDate, t.debitGLAccountID, t.creditGLAccountID
			from dbo.tr_transactions as t
			inner join dbo.tr_relationships as r on r.orgID = @origSaleOwnedByOrgID 
				and r.typeID = @tr_DITSaleTrans 
				and r.transactionID = t.transactionID 
				and r.appliedToTransactionID = @AdjToMakeSaleAdjTID
			inner join dbo.tr_transactionDIT as dit on dit.orgID = @origSaleOwnedByOrgID and dit.transactionID = t.transactionID and dit.isActive = 1
			inner join dbo.tr_transactionSales as ts on ts.orgID = @origSaleOwnedByOrgID and ts.transactionID = t.transactionID
			where t.ownedByOrgID = @origSaleOwnedByOrgID
			and t.statusID = 1;

			-- update pct split and respread amt
			select @AdjToMakeDITSum = sum(ditAmount) from @tblDITSpread where saleAdjTID = @AdjToMakeSaleAdjTID;
			update @tblDITSpread set pct = ditAmount / @AdjToMakeDITSum where saleAdjTID = @AdjToMakeSaleAdjTID;
			update @tblDITSpread set respreadAmount = cast((@AdjToMakeDITSum-abs(@AdjToMakeAmt)) * pct as numeric(18,2)) where saleAdjTID = @AdjToMakeSaleAdjTID;

			-- put any remainders on the first respread
			select @respreadSum = sum(respreadAmount), @respreadFirstAutoID = min(autoid) from @tblDITSpread where saleAdjTID = @AdjToMakeSaleAdjTID;
			set @respreadDiff = @AdjToMakeDITSum - abs(@AdjToMakeAmt) - @respreadSum;
			if @respreadDiff <> 0
				update @tblDITSpread set respreadAmount = respreadAmount + @respreadDiff where autoID = @respreadFirstAutoID;

			-- If transaction has any non-recognized revenue, we need to recognize it all now.
			select @ditspreadAutoID = min(autoID) from @tblDITSpread where saleAdjTID = @AdjToMakeSaleAdjTID;
			while @ditspreadAutoID is not null begin
				select @ditSpreadDITCacheAmount = ditCacheAmount*-1, @ditSpreadDITTID = ditTransactionID,
					@ditSpreadRecogDt = recogDt, @ditSpreadDebitGL = ditdebitGLAID, @ditSpreadCreditGL = ditcreditGLAID
				from @tblDITSpread
				where autoID = @ditspreadAutoID;

				if abs(@ditSpreadDITCacheAmount) > 0
					EXEC dbo.tr_createTransaction_dit @recordedOnSiteID=@recordedOnSiteID, @recordedByMemberID=@recordedByMemberID, 
						@statsSessionID=@statsSessionID, @amount=@ditSpreadDITCacheAmount, @transactionDate=@transactionDate, 
						@recognitionDate=null, @debitGLAccountID=@ditSpreadCreditGL, @creditGLAccountID=@ditSpreadDebitGL, 
						@saleTransactionID=@AdjToMakeSaleAdjTID, @DITTransactionID=@ditSpreadDITTID, @batchAsRecogJob=0, 
						@bypassInBounds=0, @transactionID=@newdittransactionID OUTPUT;

				update dbo.tr_transactionDIT
				set isActive = 0
				where orgID = @origSaleOwnedByOrgID
				and transactionID = @ditSpreadDITTID;

				select @ditspreadAutoID = min(autoID) from @tblDITSpread where saleAdjTID = @AdjToMakeSaleAdjTID and autoID > @ditspreadAutoID;
			end

			-- see if we need to deallocate. only need to deallocate if adj amount is gt than unallocated amt of sale/adj
			select @AdjToMakeUnallocatedAmt = cache_invoiceAmountAfterAdjustment - cache_activePaymentAllocatedAmount - cache_pendingPaymentAllocatedAmount
			from dbo.tr_invoiceTransactions
			where orgID = @origSaleOwnedByOrgID
			and transactionID = @AdjToMakeSaleAdjTID;

			if abs(@AdjToMakeAmt) > @AdjToMakeUnallocatedAmt BEGIN
				delete from @tblAllocations;

				-- get all active allocations to this sale/adj in reverse order
				INSERT INTO @tblAllocations (transactionID, PITTaxTID, allocAmount, allocDate, detail, transactionDate)
				EXEC dbo.tr_getAllocatedPaymentsofSaleOrAdj @orgID=@origSaleOwnedByOrgID, @limitToSaleTransactionID=@AdjToMakeSaleAdjTID;

				select @amtLeftToDeallocate = abs(@AdjToMakeAmt)-@AdjToMakeUnallocatedAmt;
				set @a_autoid = null;
				select @a_autoid = min(autoid) from @tblAllocations;
				while @a_autoid is not null BEGIN
					select @a_allocAmount=allocAmount, @a_paymentTID=transactionID
					from @tblAllocations 
					where autoID = @a_autoid;

					-- if amt left can be deallocated in full from this payment, take full amt. else take what we can.
					if @a_allocAmount < @amtLeftToDeallocate
						set @amountToAllocate = @a_allocAmount;
					ELSE
						set @amountToAllocate = @amtLeftToDeallocate;

					EXEC dbo.tr_deallocateFromSaleOrAdj @recordedOnSiteID=@recordedOnSiteID, @recordedByMemberID=@recordedByMemberID, 
						@statsSessionID=@statsSessionID, @amount=@amountToAllocate, @transactionDate=@transactionDate, 
						@paymentTransactionID=@a_paymentTID, @saleTransactionID=@AdjToMakeSaleAdjTID;

					set @amtLeftToDeallocate = @amtLeftToDeallocate - @amountToAllocate;
					IF @amtLeftToDeallocate <= 0
						BREAK;

					select @a_autoid = min(autoid) from @tblAllocations where autoID > @a_autoid;
				end
			end

			select @AdjToMakeAutoID = min(autoid) from @tblAdjToMake where autoID > @AdjToMakeAutoID;
		end

		-- sum and group transactions by saleTID. these are the adj transactions to record
		insert into @tblAdjToMakeFinal (saleTransactionID, isSale, debitGLAID, detail, amountToAdjust)
		select saleTransactionID, isSale, debitGLAID, detail, sum(amountToAdjust)
		from @tblAdjToMake
		group by saleTransactionID, isSale, debitGLAID, detail
		having sum(amountToAdjust) <> 0
		order by min(autoid);

		-- loop over the final adjustments to make. 
		set @AdjToMakeAutoID = null;
		select @AdjToMakeAutoID = min(autoid) from @tblAdjToMakeFinal;
		while @AdjToMakeAutoID is not null BEGIN
			select	@AdjToMakeTID=saleTransactionID, @AdjToMakeAmt=amountToAdjust, 
					@AdjToMakeIsSale=issale, @AdjToMakeGLAID=debitGLAID, @AdjToMakeDetail=detail
			from @tblAdjToMakeFinal 
			where autoid = @AdjToMakeAutoID;

			-- insert adj into transactions (ensure amount is abs)
			INSERT INTO dbo.tr_transactions (ownedByOrgID, recordedOnSiteID, statusID, detail, parentTransactionID, 
				amount, dateRecorded, transactionDate, assignedToMemberID, recordedByMemberID, statsSessionID, 
				typeID, debitGLAccountID, creditGLAccountID)
			VALUES (@origSaleOwnedByOrgID, @recordedOnSiteID, @ts_active, @AdjToMakeDetail, null, 
				abs(@AdjToMakeAmt), getdate(), @transactionDate, @origSaleAssignedToMemberID, @recordedByMemberID, @statsSessionID, 
				dbo.fn_tr_getTypeID('Adjustment'), @AdjToMakeGLAID, @ARGLAccountID);
			IF @AdjToMakeIsSale = 1 BEGIN
				select @AdjSaleTransactionID = SCOPE_IDENTITY();
				SET @transactionID = @AdjSaleTransactionID;
			END ELSE
				select @AdjTaxTransactionID = SCOPE_IDENTITY();

			-- insert adj into relationships
			-- tie tax to the sale adjustment
			IF @AdjToMakeIsSale = 0	BEGIN
				INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID, orgID)
				VALUES (@tr_AdjustTrans, @AdjTaxTransactionID, @AdjToMakeTID, @origSaleOwnedByOrgID);

				INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID, orgID)
				VALUES (@tr_PITTaxTrans, @AdjTaxTransactionID, @AdjSaleTransactionID, @origSaleOwnedByOrgID);
			END
			ELSE 
				INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID, orgID)
				VALUES (@tr_AdjustTrans, @AdjSaleTransactionID, @AdjToMakeTID, @origSaleOwnedByOrgID);

			-- put adj on invoice (0 dollars.. no neg amounts here)
			IF @AdjToMakeIsSale = 1 BEGIN			
				SET @contentVersionID = null;
				SELECT @contentVersionID = max(cv.contentVersionID)
					FROM dbo.tr_glAccounts as gl
					INNER JOIN dbo.cms_content as c on c.contentID = gl.invoiceContentID
					INNER JOIN dbo.cms_siteResources sr	on sr.siteResourceID = c.siteResourceID 
					INNER JOIN dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID and srs.siteResourceStatusDesc = 'Active'
					INNER JOIN dbo.cms_contentLanguages as cl ON cl.contentID = c.contentID AND cl.languageID = 1
					INNER JOIN dbo.cms_contentVersions as cv on cv.contentLanguageID = cl.contentLanguageID
					WHERE gl.orgID = @origSaleOwnedByOrgID
					and gl.GLAccountID = @AdjToMakeGLAID
					AND cv.isActive = 1
					AND len(cv.rawContent) > 0;
				INSERT INTO dbo.tr_invoiceTransactions (transactionID, invoiceID, cache_invoiceAmountAfterAdjustment, cache_activePaymentAllocatedAmount, cache_pendingPaymentAllocatedAmount, messageContentVersionID, orgID)
				VALUES (@AdjSaleTransactionID, @invoiceID, 0, 0, 0, @contentVersionID, @origSaleOwnedByOrgID);
			END 
			ELSE 
				INSERT INTO dbo.tr_invoiceTransactions (transactionID, invoiceID, cache_invoiceAmountAfterAdjustment, cache_activePaymentAllocatedAmount, cache_pendingPaymentAllocatedAmount, orgID)
				VALUES (@AdjTaxTransactionID, @invoiceID, 0, 0, 0, @origSaleOwnedByOrgID);

			IF @AdjToMakeIsSale = 1 begin	
				update @tblAdjToMake set adjTransactionID = @AdjSaleTransactionID where saleTransactionID = @AdjToMakeTID;
				update @tblAdjToMakeFinal set adjTransactionID = @AdjSaleTransactionID where autoID = @AdjToMakeAutoID;
			end ELSE begin
				update @tblAdjToMake set adjTransactionID = @AdjTaxTransactionID where saleTransactionID = @AdjToMakeTID;
				update @tblAdjToMakeFinal set adjTransactionID = @AdjTaxTransactionID where autoID = @AdjToMakeAutoID;
			end		

			select @AdjToMakeAutoID = min(autoid) from @tblAdjToMakeFinal where autoid > @AdjToMakeAutoID;
		end

		-- update tr_invoiceTransaction cache, add AdjustInvTrans relationship, and cleanup invoices
		update it
		set it.cache_invoiceAmountAfterAdjustment = it.cache_invoiceAmountAfterAdjustment - abs(tbl.amountToAdjust)
		from dbo.tr_invoiceTransactions as it
		inner join @tblAdjToMake as tbl on tbl.saleAdjTID = it.transactionID and tbl.amountToAdjust <> 0
		where it.orgID = @origSaleOwnedByOrgID;

		INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID, amount, orgID)
		SELECT @tr_AdjustInvTrans, tbl.adjTransactionID, tbl.saleAdjTID, abs(tbl.amountToAdjust), @origSaleOwnedByOrgID
		FROM @tblAdjToMake as tbl
		WHERE tbl.adjTransactionID is not null;

		-- cleanup invoices to set correct closed/delinq/paid status
		declare @invoiceIDList varchar(max);
		select @invoiceIDList = COALESCE(@invoiceIDList + ',', '') + cast(i.invoiceID as varchar(20)) 
			from @tblAdjToMake as a
			inner join dbo.tr_invoices as i on i.orgID = @origSaleOwnedByOrgID and i.invoiceID = a.SaleAdjInvoiceID
			inner join dbo.tr_invoiceStatuses as ins on ins.statusID = i.statusID
			where ins.status not in ('Open','Pending');
		IF @invoiceIDList is not null
			EXEC dbo.tr_closeInvoice @orgID=@origSaleOwnedByOrgID, @enteredByMemberID=@recordedByMemberID, @invoiceIDList=@invoiceIDList;

		-- update tr_transactionSales cache, add AdjustTSaleTrans relationship
		update ts
		set ts.cache_amountAfterAdjustment = ts.cache_amountAfterAdjustment - abs(tbl.amountToAdjust)
		from dbo.tr_transactionSales as ts
		inner join @tblAdjToMakeFinal as tbl on tbl.saleTransactionID = ts.transactionID
		where ts.orgID = @origSaleOwnedByOrgID;

		INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID, amount, orgID)
		SELECT @tr_AdjustTSaleTrans, tbl.adjTransactionID, tbl.saleTransactionID, abs(tbl.amountToAdjust), @origSaleOwnedByOrgID
		FROM @tblAdjToMakeFinal as tbl;

		-- respread any amounts over recognition dates. respread 0 amounts as well to serve as placeholders.
		select @respreadAutoID = min(autoID) from @tblDITSpread;
		while @respreadAutoID is not null begin
			set @ditTransactionID = null;

			select @amtToRespread=respreadAmount, @ditSpreadRecogDt=recogDt, @respreadDebitGL=ditdebitGLAID,
				@respreadCreditGL=ditcreditGLAID, @respreadsaleAdjTID=saleAdjTID
			from @tblDITSpread 
			where autoID = @respreadAutoID;
		
			IF @ditSpreadRecogDt < getdate()
				SET @bypassInBounds = 1;
			ELSE
				SET @bypassInBounds = 0;
			
			-- put all scheduled rows here for auditing, even if they are immediately recognized
			EXEC dbo.tr_createTransaction_dit @recordedOnSiteID=@recordedOnSiteID, 
				@recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID, 
				@amount=@amtToRespread, @transactionDate=@transactionDate, @recognitionDate=@ditSpreadRecogDt, 
				@debitGLAccountID=@respreadDebitGL, @creditGLAccountID=@respreadCreditGL, 
				@saleTransactionID=@respreadsaleAdjTID, @DITTransactionID=null, @batchAsRecogJob=0, 
				@bypassInBounds=@bypassInBounds, @transactionID=@ditTransactionID OUTPUT;

			-- if recognition date is today or in past, then move it out immediately back to revenue
			IF @ditSpreadRecogDt < getdate() BEGIN
				set @amtToRespread = @amtToRespread * -1;
				EXEC dbo.tr_createTransaction_dit @recordedOnSiteID=@recordedOnSiteID, 
					@recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID, 
					@amount=@amtToRespread, @transactionDate=@transactionDate, @recognitionDate=null, 
					@debitGLAccountID=@respreadCreditGL, @creditGLAccountID=@respreadDebitGL, 
					@saleTransactionID=@respreadsaleAdjTID, @DITTransactionID=@ditTransactionID, @batchAsRecogJob=0, 
					@bypassInBounds=@bypassInBounds, @transactionID=@ditTransactionID2 OUTPUT;
			END

			select @respreadAutoID = min(autoID) from @tblDITSpread where autoID > @respreadAutoID;
		end

		-- in-bounds checking
		-- get tree of sale/adj. no amounts can be negative.
		INSERT INTO @tblTransOutOfOrder (transactionID)
		select saleTransactionID
		from (
			select saleTransactionID, SUM(amount) OVER (PARTITION BY saleTransactionID ORDER BY transactionDate, anyTID) as runningTotal
			from (
				select @saleTransactionID as saleTransactionID, @saleTransactionID as anyTID, t.transactionDate, t.amount
				from dbo.tr_transactions as t
				where t.transactionID = @saleTransactionID
					union
				select @saleTransactionID, tAdj.transactionID, tAdj.transactionDate, 
					case when tAdj.creditGLAccountID = @ARGLAccountID then tAdj.amount * -1 else tAdj.amount end
				from dbo.tr_relationships as trAdj
				inner join dbo.tr_transactions as tAdj on tAdj.ownedByOrgID = @origSaleOwnedByOrgID and tAdj.transactionID = trAdj.transactionID and tAdj.statusID = 1
				where trAdj.orgID = @origSaleOwnedByOrgID
				and trAdj.typeID = @tr_AdjustTrans
				and trAdj.appliedToTransactionID = @saleTransactionID
			) as tmpOuter
		) as tree
		where runningTotal < 0;

		IF @@ROWCOUNT > 0
			RAISERROR('Negative adjustment violates the in-bounds checking.', 16, 1);

		IF @origSaleItemID IS NOT NULL
			EXEC dbo.tr_createInvoiceItem @orgID=@origSaleOwnedByOrgID, @siteID=@recordedOnSiteID, @invoiceID=@invoiceID, @itemID=@origSaleItemID, @itemType=@origSaleItemType;

		-- add adjustment to limit checking table
		INSERT INTO platformQueue.dbo.queue_transactionLimitFlagging (transactionID, orgID, siteID)
		VALUES (@transactionID, @origSaleOwnedByOrgID, @recordedOnSiteID);
	COMMIT TRAN;
	
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
