ALTER PROC dbo.tr_createTransaction_voidOffset
@recordedOnSiteID int,
@recordedByMemberID int,
@statsSessionID int,
@appliedToTransactionID int,
@vidPool xml OUTPUT,
@transactionID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @assignedToMemberID int, @ownedByOrgID int, @typeID int, @nowDate datetime, @originvoiceID int, 
		@originvoiceProfileID int, @origInvoiceStatusID int, @invoiceID int, @invoiceNumber varchar(18),
		@isWOPayment bit, @batchID int, @batchCode varchar(40), @batchName varchar(400), @tr_OffsetTrans int, 
		@batchPayProfileID int, @transactionDate datetime, @depositsGLAccountID int, @t_VO int,
		@origSaleItemID int, @origSaleItemType varchar(30);

	set @transactionID = 0;

	-- ensure @appliedToTransactionID is not voided and not of type VoidOffset
	IF EXISTS (select transactionID from dbo.tr_transactions where transactionID = @appliedToTransactionID and (statusID not in (1,3) or typeID = 8))
		RAISERROR('appliedToTransactionID is voided or is a voidOffset', 16, 1);

	-- dont assume memberid is the active one. get the active one.
	select @assignedToMemberID = m.activeMemberID, @ownedByOrgID = t.ownedByOrgID
		from dbo.tr_transactions as t
		inner join dbo.ams_members as m on m.memberid = t.assignedToMemberID
		where t.transactionID = @appliedToTransactionID;
	select @recordedByMemberID = activeMemberID
		from dbo.ams_members
		where memberID = @recordedByMemberID;

	-- get the deposits account
	EXEC dbo.tr_getGLAccountByGLCode @orgID=@ownedByOrgID, @GLCode='DEPOSITS', @GLAccountID=@depositsGLAccountID OUTPUT;
	set @t_VO = dbo.fn_tr_getTypeID('VoidOffset');
	set @tr_OffsetTrans = dbo.fn_tr_getRelationshipTypeID('OffsetTrans');

	select @typeID = typeID from dbo.tr_transactions where transactionID = @appliedToTransactionID;
	select @nowDate = getdate();

	BEGIN TRAN;
		-- insert into transactions with debit/credit accounts switched
		INSERT INTO dbo.tr_transactions (ownedByOrgID, recordedOnSiteID, statusID, detail, parentTransactionID, 
			amount, dateRecorded, transactionDate, assignedToMemberID, recordedByMemberID, statsSessionID, 
			typeID, debitGLAccountID, creditGLAccountID)
		SELECT top 1 @ownedByOrgID, @recordedOnSiteID, dbo.fn_tr_getStatusID('Active'), detail, null, 
			amount, getdate(), getdate(), @assignedToMemberID, @recordedByMemberID, @statsSessionID, 
			@t_VO, creditGLAccountID, debitGLAccountID
		FROM dbo.tr_transactions
		where transactionID = @appliedToTransactionID;
			select @transactionID = SCOPE_IDENTITY();

		-- insert into relationships
		INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID, orgID)
		VALUES (@tr_OffsetTrans, @transactionID, @appliedToTransactionID, @ownedByOrgID);

		/* 
		Put on invoice if voiding 1,3,7
		1. put VO on same invoice as Transaction if invoice is open/pending.
		2. put VO on first invoice from vidpool that matches invoice profile.
		3. put VO on new invoice if necessary
		*/
		IF @typeID in (1,3,7) BEGIN
			select @originvoiceid = it.invoiceid, @originvoiceProfileID = i.invoiceProfileID,
				@origInvoiceStatusID = i.statusID
			from dbo.tr_invoiceTransactions as it
			inner join dbo.tr_invoices as i on i.invoiceID = it.invoiceID
			where it.orgID = @ownedByOrgID
			and it.transactionID = @appliedToTransactionID;

			SELECT TOP 1 @origSaleItemID = itemID, @origSaleItemType = itemType
			FROM dbo.tr_invoiceItems
			WHERE orgID = @ownedByOrgID
			AND invoiceID = @originvoiceid;

			-- case 1
			IF @origInvoiceStatusID in (1,2) begin
				set @invoiceID = @originvoiceid;
				select @vidPool = (
					select [id]
					from (
						select @invoiceID as [id]
							union				
						select V.item.value('@id','int')
						FROM @vidPool.nodes('/vpool/v') as V(item)
					) as tmp
					FOR XML RAW('v'), root('vpool')
				);
			end else begin
				-- case 2
				select top 1 @invoiceID = i.invoiceID
					FROM @vidPool.nodes('/vpool/v') as V(item)
					inner join dbo.tr_invoices as i on i.invoiceID = V.item.value('@id','int')
					where i.invoiceProfileID = @originvoiceProfileID
					order by i.invoiceid;

				-- case 3
				if @invoiceID is null begin
					EXEC dbo.tr_createInvoice @invoiceProfileID=@originvoiceProfileID, 
						@enteredByMemberID=@recordedByMemberID, @assignedToMemberID=@assignedToMemberID, 
						@dateBilled=@nowDate, @dateDue=@nowdate, 
						@invoiceID=@invoiceID OUTPUT, @invoiceNumber=@invoiceNumber OUTPUT;
					select @vidPool = (
						select [id]
						from (
							select @invoiceID as [id]
								union				
							select V.item.value('@id','int')
							FROM @vidPool.nodes('/vpool/v') as V(item)
						) as tmp
						FOR XML RAW('v'), root('vpool')
					);

					IF @origSaleItemID IS NOT NULL
						EXEC dbo.tr_createInvoiceItem @orgID=@ownedByOrgID, @siteID=@recordedOnSiteID, @invoiceID=@invoiceID, @itemID=@origSaleItemID, @itemType=@origSaleItemType;
				end
			end

			INSERT INTO dbo.tr_invoiceTransactions (transactionID, invoiceID, cache_invoiceAmountAfterAdjustment, cache_activePaymentAllocatedAmount, cache_pendingPaymentAllocatedAmount, orgID)
			VALUES (@transactionID, @invoiceID, 0, 0, 0, @ownedByOrgID);
		END

		/* 
		Put on batch if voiding 2,4,9,5,6 of Pay
		1. put VO on same batch as Transaction if batch is Open
		2. else put VO on daily _VD batch 
		-- if credit acct of this VO transaction is DEPOSITS, it is a VO of a Negative Write Off
		*/
		IF EXISTS (
			select transactionID
			from dbo.tr_transactions
			where transactionID = @transactionID
			and creditGLAccountID = @depositsGLAccountID
			) 
			SET @isWOPayment = 1;
		ELSE 	
			SET @isWOPayment = 0;
		
		IF @typeID in (2,4,9,5) OR (@typeID = 6 and @isWOPayment = 1) BEGIN
			select @batchID = b.batchID
			from dbo.tr_batches as b
			inner join dbo.tr_batchTransactions as bt on bt.orgID = b.orgID and bt.batchID = b.batchID
				and bt.transactionID = @appliedToTransactionID
			where b.orgID = @ownedByOrgID
			and b.statusID = 1;
			
			IF @batchID is null BEGIN
				select TOP 1 
					@batchPayProfileID = tmp.BatchPayProfileID,
					@batchCode = CONVERT(CHAR(8),@nowDate,112) + '_' + cast(tmp.BatchPayProfileID as varchar(10)) + '_' + cast(tmp.BatchCashGLAccountID as varchar(10)) + '_VD',
					@batchName = CONVERT(CHAR(8),@nowDate,112) + ' ' + mp.profileCode + ' ' + gl.accountName + ' Voids'
				from (
					select 
						case 
						when tVoided.typeID = 2 then tVoided.debitGLAccountID
						when tVoided.typeID = 4 then tVoided.creditGLAccountID
						when tVoided.typeID = 9 then tVoided.creditGLAccountID
						when tVoided.typeID = 5 then tPay.debitGLAccountID
						when tVoided.typeID = 6 then tPay3.debitGLAccountID
						else null
						end as BatchCashGLAccountID,
						case
						when tVoided.typeID = 2 then tpV.profileID
						when tVoided.typeID = 4 then tpV.profileID
						when tVoided.typeID = 9 then tp2.profileID
						when tVoided.typeID = 5 then tp.profileID
						when tVoided.typeID = 6 then tp3.profileID
						else null
						end as BatchPayProfileID
					from dbo.tr_transactions as tVoided
					left outer join dbo.tr_transactionPayments as tpV on tpV.orgID = @ownedByOrgID and tpV.transactioniD = tVoided.transactionID and tVoided.typeID in (2,4)
					left outer join dbo.tr_relationships as tr2
						inner join dbo.tr_transactions as tPay on tPay.ownedByOrgID = @ownedByOrgID and tPay.transactionID = tr2.appliedToTransactionID and tPay.typeID = 2
						inner join dbo.tr_transactionPayments as tp on tp.orgID = @ownedByOrgID and tp.transactioniD = tPay.transactionID
						on tr2.orgID = @ownedByOrgID and tr2.transactionID = tVoided.transactionID and tr2.typeID = 2 and tVoided.typeID = 5
					left outer join dbo.tr_relationships as tr3
						inner join dbo.tr_transactions as tPay2 on tPay2.ownedByOrgID = @ownedByOrgID and tPay2.transactionID = tr3.appliedToTransactionID and tPay2.typeID = 2
						inner join dbo.tr_transactionPayments as tp2 on tp2.orgID = @ownedByOrgID and tp2.transactioniD = tPay2.transactionID
						on tr3.orgID = @ownedByOrgID and tr3.transactionID = tVoided.transactionID and tr3.typeID = 13 and tVoided.typeID = 9
					left outer join dbo.tr_relationships as tr4
						inner join dbo.tr_transactions as tPay3 on tPay3.ownedByOrgID = @ownedByOrgID and tPay3.transactionID = tr4.appliedToTransactionID and tPay3.typeID = 2
						inner join dbo.tr_transactionPayments as tp3 on tp3.orgID = @ownedByOrgID and tp3.transactioniD = tPay3.transactionID
						on tr4.orgID = @ownedByOrgID and tr4.transactionID = tVoided.transactionID and tr4.typeID = 7 and tVoided.typeID = 6
					where tVoided.ownedByOrgID = @ownedByOrgID
					and tVoided.transactionID = @AppliedToTransactionID
				) as tmp		
				inner join dbo.tr_glAccounts as gl on gl.glAccountID = tmp.BatchCashGLAccountID
				inner join dbo.mp_profiles as mp on mp.profileID = tmp.BatchPayProfileID;
			
				select @batchID = b.batchID
					from dbo.tr_batches as b
					where b.orgID = @ownedByOrgID
					and b.batchTypeID = 3
					and b.batchCode = @batchCode
					and b.statusID = 1
					and b.isSystemCreated = 1;
				IF @batchID is null
					EXEC dbo.tr_createBatch @orgID=@ownedByOrgID, @payProfileID=@batchPayProfileID, 
						@batchTypeID=3, @batchCode=@batchCode, @batchName=@batchName, @controlAmt=0, @controlCount=0, 
						@depositDate=@nowDate, @isSystemCreated=1, @createdByMemberID=null, @batchID=@batchID OUTPUT;
			END

			INSERT INTO dbo.tr_batchTransactions (orgID, batchID, transactionID)
			VALUES (@ownedByOrgID, @batchID, @transactionID);
		END
	COMMIT TRAN;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
