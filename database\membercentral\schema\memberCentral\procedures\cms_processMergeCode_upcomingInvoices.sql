CREATE PROC dbo.cms_processMergeCode_upcomingInvoices
@maxRows int,
@memberID int,
@orgID int,
@orgcode varchar(10),
@invoiceProfileList varchar(1000)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	IF OBJECT_ID('tempdb..#tmpInvoiceProfile') IS NOT NULL
		DROP TABLE #tmpInvoiceProfile;	
	IF OBJECT_ID('tempdb..#tmpInvoices') IS NOT NULL 
		DROP TABLE #tmpInvoices;
	CREATE TABLE #tmpInvoiceProfile (invoiceProfileID int PRIMARY KEY, profileName varchar(50));

	IF len(@invoiceProfileList) > 0
		INSERT INTO #tmpInvoiceProfile (invoiceProfileID, profileName) 
		SELECT distinct inp.profileID, inp.profileName
		FROM dbo.tr_invoiceProfiles as inp
		inner join dbo.fn_VarCharListToTable(@invoiceProfileList,'|') as li on li.listItem = inp.profileName
		where inp.orgID = @orgID;
	ELSE
		INSERT INTO #tmpInvoiceProfile (invoiceProfileID, profileName) 
		SELECT profileID, profileName
		FROM dbo.tr_invoiceProfiles
		where orgID = @orgID;

	select i.invoiceID, i.dateDue, @orgcode + dbo.fn_tr_padInvoiceNumber(i.invoiceNumber) as invoiceNumber, 
		i.invoiceCode, tip.profileName
	into #tmpInvoices
	from dbo.tr_invoices as i
	inner join #tmpInvoiceProfile as tip ON tip.invoiceProfileID = i.invoiceProfileID
	inner join dbo.ams_members as m on m.orgID = @orgID 
		and m.memberID = i.assignedToMemberID
		and m.activeMemberID = @memberID
	where i.orgID = @orgID
	and i.statusID in (3,5)
	and i.dateDue > getdate();

	select top (@maxrows) i.invoiceID, i.dateDue, i.invoiceNumber, i.profileName, i.invoiceCode, 
		sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount) as invDue
	from #tmpInvoices as i
	inner join dbo.tr_invoiceTransactions as it on it.orgID = @orgID and it.invoiceID = i.invoiceID
	group by i.invoiceID, i.dateDue, i.invoiceNumber, i.profileName, i.invoiceCode
	having sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount) > 0
	order by i.dateDue, i.invoiceID;

	IF OBJECT_ID('tempdb..#tmpInvoiceProfile') IS NOT NULL
		DROP TABLE #tmpInvoiceProfile;	
	IF OBJECT_ID('tempdb..#tmpInvoices') IS NOT NULL 
		DROP TABLE #tmpInvoices;

	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
