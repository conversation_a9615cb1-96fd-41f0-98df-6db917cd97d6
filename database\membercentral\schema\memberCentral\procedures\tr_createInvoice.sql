ALTER PROC dbo.tr_createInvoice
@invoiceProfileID int,
@enteredByMemberID int, 
@assignedToMemberID int, 
@dateBilled datetime,
@dateDue datetime,
@invoiceID int OUTPUT,
@invoiceNumber varchar(18) OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @statusID int, @invoicenum int, @orgID int, @orgcode varchar(10), @invoiceCode char(8), 
		@expectedPayDate date, @invNumSql nvarchar(100);

	select @invoiceID = 0, @invoiceNumber = '';
	select @statusID = statusID from dbo.tr_invoiceStatuses where [status] = 'Open';

	-- dateDue and dateBilled should be 00:00:00 of passed in date
	select @dateDue = cast(@dateDue as date);
	select @dateBilled = cast(@dateBilled as date);

	-- dont assume memberid is the active one. get the active one.
	select @assignedToMemberID=m.activeMemberID, @orgID=o.orgID, @orgcode=o.orgcode
	from dbo.ams_members as m
	inner join dbo.organizations as o on o.orgID = m.orgID
	where m.memberID = @assignedToMemberID;

	EXEC dbo.getUniqueCode @uniqueCode=@invoiceCode OUTPUT;

	-- expectedPayDate cannot be in the past.
	IF @dateDue < getdate()
		SET @expectedPayDate = getdate();
	ELSE
		SET @expectedPayDate = @dateDue;
	
	-- query to get next invoicenumber from org sequence
	SET @invNumSql = N'SELECT @invoiceNumOUT = NEXT VALUE FOR dbo.tr_invoiceNum_' + @orgcode;
	EXEC sp_executesql @invNumSql, N'@invoiceNumOUT int OUTPUT', @invoiceNumOUT=@invoiceNum OUTPUT;  

	BEGIN TRAN;	
		INSERT INTO dbo.tr_invoices (datecreated, dateBilled, dateDue, assignedToMemberID, orgID, statusID, invoiceNumber, 
			invoiceCode, invoiceProfileID, expectedPayDate)
		VALUES (getdate(), @dateBilled, @dateDue, @assignedToMemberID, @orgID, @statusID, @invoicenum, @invoiceCode, 
			@invoiceProfileID, @expectedPayDate);
		select @invoiceID = SCOPE_IDENTITY();

		insert into dbo.tr_invoiceStatusHistory (invoiceID, updateDate, statusID, oldStatusID, enteredByMemberID)
		values(@invoiceID, getdate(), 1, null, @enteredByMemberID);
	COMMIT TRAN;

	-- pad invoice number
	select @invoiceNumber = @orgcode + dbo.fn_tr_padInvoiceNumber(@invoicenum);
		
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
