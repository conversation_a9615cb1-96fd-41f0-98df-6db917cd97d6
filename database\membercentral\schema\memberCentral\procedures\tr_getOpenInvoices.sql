ALTER PROC dbo.tr_getOpenInvoices
@orgID int,
@limitAlertsTo int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
				
	declare @SevenDaysAgo datetime = dateadd(d,-7,getdate()), @openInvStatus int, @orgcode varchar(10),
		@totalCount int;
	select @openInvStatus = statusID from dbo.tr_invoiceStatuses where [status] = 'Open';
	select @orgcode = orgcode from dbo.organizations where orgID = @orgID;

	IF OBJECT_ID('tempdb..#tblOpenInv') IS NOT NULL 
		DROP TABLE #tblOpenInv;
	CREATE TABLE #tblOpenInv (invoiceID int PRIMARY KEY);

	INSERT INTO #tblOpenInv (invoiceID)
	select i.invoiceID
	from dbo.tr_invoices as i
	where i.orgID = @orgID
	and i.statusID = @openInvStatus
	and i.dateDue < @SevenDaysAgo;

	set @totalCount = @@ROWCOUNT;

	select top (@limitAlertsTo) i.dateDue, @orgcode + dbo.fn_tr_padInvoiceNumber(i.invoiceNumber) as invoiceNumber,
		m2.memberid, m2.lastname, m2.firstname, m2.membernumber, m2.company, 
		case when i.payProfileID is null then 0 else 1 end as hasCard,
		isnull(sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount),0) as InvDue,
		@totalCount as totalCount
	from #tblOpenInv as tmp
	inner join dbo.tr_invoices as i on i.orgID = @orgID and i.invoiceID = tmp.invoiceID
	inner join dbo.ams_members as m on m.orgid = @orgID and m.memberid = i.assignedToMemberID
	inner join dbo.ams_members as m2 on m2.orgid = @orgID and m2.memberid = m.activeMemberID
	left outer join dbo.tr_invoiceTransactions as it on it.orgID = @orgID and it.invoiceID = i.invoiceID
	group by i.dateDue, i.invoiceNumber, m2.memberid, m2.lastname, m2.firstname, m2.membernumber, m2.company, i.payProfileID
	order by m2.lastname, m2.firstname, m2.membernumber, i.dateDue;

	IF OBJECT_ID('tempdb..#tblOpenInv') IS NOT NULL 
		DROP TABLE #tblOpenInv;

	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1
END CATCH
GO
