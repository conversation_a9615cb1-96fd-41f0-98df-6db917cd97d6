ALTER PROC dbo.tr_report_arDetail
@orgID int,
@asOfDate datetime,
@GLAccountID int,
@filename varchar(400)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	declare @orgcode varchar(10);
	select @orgcode = orgcode from dbo.organizations where orgID = @orgID;
	
	IF OBJECT_ID('tempdb..#tmpARReport') IS NOT NULL 
		DROP TABLE #tmpARReport;
	CREATE TABLE #tmpARReport (revenueGLAccountID int, invoiceID int, EndAmount decimal(18,2));

	INSERT INTO #tmpARReport (revenueGLAccountID, invoiceID, EndAmount)
	EXEC dbo.tr_report_baseAR @orgID=@orgID, @asOfDate=@asOfDate, @startdate=@asOfDate, @sumBy='glinv';

	DECLARE @selectsql varchar(max) = '
		select gl.AccountName, gl.AccountCode, ''' + @orgcode + ''' + dbo.fn_tr_padInvoiceNumber(i.invoiceNumber) as InvoiceNumber, 
			i.dateCreated, i.dateBilled, i.dateDue, tmp.endAmount as ARAmount, 
			m2.lastName + '', '' + m2.firstName + '' ('' + m2.MemberNumber + '')'' as AssignedToMember, 
			m2.company as Company, ROW_NUMBER() OVER(order by i.invoiceNumber) as mcCSVorder 
		*FROM* #tmpARReport as tmp
		inner join dbo.tr_GLAccounts as gl on gl.orgID = ' + cast(@orgID as varchar(10)) + ' and gl.GLAccountID = tmp.revenueGLAccountID
		inner join dbo.tr_invoices as i on i.orgID = ' + cast(@orgID as varchar(10)) + ' and i.invoiceID = tmp.invoiceID
		inner join dbo.ams_members as m on m.orgID = ' + cast(@orgID as varchar(10)) + ' and m.memberID = i.assignedToMemberID
		inner join dbo.ams_members as m2 on m2.orgID = ' + cast(@orgID as varchar(10)) + ' and m2.memberID = m.activeMemberID ';
	IF @GLAccountID IS NOT NULL
		SET @selectsql = @selectsql + 'where gl.GLAccountID = ' + cast(@GLAccountID as varchar(10)) + ' ';
	SET @selectsql = @selectsql + 'order by gl.AccountName, i.invoiceNumber';
	EXEC dbo.up_queryToCSV @selectsql=@selectsql, @csvfilename=@filename, @returnColumns=0;

	IF OBJECT_ID('tempdb..#tmpARReport') IS NOT NULL 
		DROP TABLE #tmpARReport;

	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
