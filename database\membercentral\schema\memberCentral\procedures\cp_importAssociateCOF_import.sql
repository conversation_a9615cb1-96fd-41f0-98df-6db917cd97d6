ALTER PROC dbo.cp_importAssociateCOF_import
@siteID int,
@programID int,
@statusCodeList varchar(30),
@recordedByMemberID int,
@importResult xml OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @tblCPStatusIDs TABLE (statusID int);
	declare @orgID int, @payProfileIDList varchar(max);
	select @orgID = orgID from dbo.sites where siteID = @siteID;

	IF OBJECT_ID('tempdb..#tmpCPCardOnFiles') IS NOT NULL 
		DROP TABLE #tmpCPCardOnFiles;
	IF OBJECT_ID('tempdb..#tmpAuditLog') IS NOT NULL 
		DROP TABLE #tmpAuditLog;
	IF OBJECT_ID('tempdb..#tmpNewCPPayProfiles') IS NOT NULL 
		DROP TABLE #tmpNewCPPayProfiles;
	IF OBJECT_ID('tempdb..#tmpUpdatedInvoices') IS NOT NULL 
		DROP TABLE #tmpUpdatedInvoices;
	IF OBJECT_ID('tempdb..#tmpChangedPayProfiles') IS NOT NULL 
		DROP TABLE #tmpChangedPayProfiles;
	CREATE TABLE #tmpCPCardOnFiles (contributionID int, payProfileID int, MPProfileID int);
	CREATE TABLE #tmpAuditLog (auditCode varchar(10), msg varchar(max));
	CREATE TABLE #tmpNewCPPayProfiles (contributionPayProfileID int);
	CREATE TABLE #tmpUpdatedInvoices (invoiceID int);
	CREATE TABLE #tmpChangedPayProfiles (payProfileID int);

	BEGIN TRY
		INSERT INTO @tblCPStatusIDs (statusID)
		select cps.statusID
		from dbo.cp_statuses as cps
		inner join dbo.fn_varcharListToTable(@statusCodeList,',') as tmp on tmp.listitem = cps.statusCode;

		INSERT INTO #tmpCPCardOnFiles (contributionID, payProfileID, MPProfileID)
		select c.contributionID, tmp.MCMemberPayProfileID, tmp.MCProfileID
		from #cp_ContributorCOFImport as tmp
		inner join dbo.ams_members as m on m.orgID = @orgID and m.activeMemberID = tmp.MCMemberID
		inner join dbo.cp_contributions as c on c.memberID = m.memberID and c.programID = @programID
		inner join dbo.cp_statuses as cps on cps.statusID = c.statusID
		inner join @tblCPStatusIDs as tmpStat on tmpStat.statusID = cps.statusID;

		INSERT INTO #tmpAuditLog (auditCode, msg)
		SELECT DISTINCT 'CP', 'Pay Profile ' + mpp.detail + ' removed from Contribution Program [' 
			+ cp.programName + ISNULL(' - ' + cpc.campaignName,'') + '] (ContributionID: ' + CAST(c.contributionID AS varchar(10))')'
		FROM dbo.cp_contributionPayProfiles AS cpp 
		INNER JOIN #tmpCPCardOnFiles as tmp on tmp.contributionID = cpp.contributionID
		INNER JOIN dbo.cp_contributions AS c ON c.contributionID = cpp.contributionID
		INNER JOIN dbo.cp_programs AS cp ON cp.programID = c.programID
		INNER JOIN dbo.ams_memberPaymentProfiles as mpp on mpp.payProfileID = cpp.payProfileID
		LEFT OUTER JOIN dbo.cp_campaigns AS cpc ON cpc.campaignID = c.campaignID;

		-- get existing payprofileIDs for conditions reprocessing
		INSERT INTO #tmpChangedPayProfiles (payProfileID)
		SELECT DISTINCT cpp.payProfileID
		FROM dbo.cp_contributionPayProfiles as cpp 
		INNER JOIN #tmpCPCardOnFiles as tmp on tmp.contributionID = cpp.contributionID;

		BEGIN TRAN;
			DELETE cpp
			FROM dbo.cp_contributionPayProfiles as cpp 
			INNER JOIN #tmpCPCardOnFiles as tmp on tmp.contributionID = cpp.contributionID;

			INSERT INTO dbo.cp_contributionPayProfiles (contributionID, MPProfileID, payProfileID, invoiceProfileID)
				OUTPUT INSERTED.contributionPayProfileID INTO #tmpNewCPPayProfiles
			select distinct tmp.contributionID, tmp.merchantProfileID, tmp.payProfileID, tmp.profileID
			from (
				select distinct c.contributionID, tmp.MPProfileID as merchantProfileID, tmp.payProfileID, ip.profileID
				from dbo.cp_distributions as cpd
				inner join dbo.cp_programs as cp on cp.programID = cpd.programID
				inner join dbo.cp_contributions as c on c.programID = cp.programID
				inner join #tmpCPCardOnFiles as tmp on tmp.contributionID = c.contributionID
				inner join dbo.tr_GLAccounts as gl on gl.orgID = @orgID and gl.GLAccountID = cpd.GLAccountID
				inner join dbo.tr_invoiceProfiles as ip on ip.orgID = @orgID and ip.profileID = gl.invoiceProfileID
					union all
				select distinct c.contributionID, tmp.MPProfileID as merchantProfileID, tmp.payProfileID, ip.profileID
				from dbo.cf_fieldData as fd
				inner join dbo.cf_fields as f on f.fieldID = fd.fieldID
				inner join dbo.cf_fieldTypes as ft on ft.fieldTypeID = f.fieldTypeID
				inner join dbo.cp_programs as p on p.programID = f.detailID 
					and f.controllingSiteResourceID = p.siteResourceID
				inner join dbo.cp_contributions as c on c.programID = p.programID
				inner join #tmpCPCardOnFiles as tmp on tmp.contributionID = c.contributionID 
					and fd.itemID = tmp.contributionID
				inner join dbo.tr_GLAccounts as gl on gl.orgID = @orgID and gl.GLAccountID = f.GLAccountID
				inner join dbo.tr_invoiceProfiles as ip on ip.orgID = @orgID and ip.profileID = gl.invoiceProfileID
				left outer join dbo.cf_fieldValues as fv on fv.fieldID = f.fieldID 
					and fv.valueID = fd.valueID
					and ft.displayTypeCode in ('SELECT','RADIO','CHECKBOX')
				where fd.itemType = 'ContributionProgram'
				and f.isActive = 1
				and case 
					when ft.displayTypeCode = 'TEXTBOX' and ( (ft.supportQty = 1 and f.amount > 0) or ft.supportAmt = 1 )  then 1
					when ft.displayTypeCode in ('SELECT','RADIO','CHECKBOX') and fv.amount > 0 then 1
				else 0 end = 1
			) tmp;

			-- get new payprofileIDs for conditions reprocessing
			INSERT INTO #tmpChangedPayProfiles (payProfileID)
			SELECT DISTINCT cpp.payProfileID
			FROM dbo.cp_contributionPayProfiles as cpp 
			INNER JOIN #tmpCPCardOnFiles as tmp on tmp.contributionID = cpp.contributionID;

			-- trigger reprocessing of credit card expiration conditions (if limiting to contributions)
			SELECT @payProfileIDList = COALESCE(@payProfileIDList + ',', '') + cast(payProfileID as varchar(20))
			FROM #tmpChangedPayProfiles
			GROUP BY payProfileID;

			IF @payProfileIDList IS NOT NULL BEGIN
				EXEC dbo.tr_reprocessCCExpConditions @orgID=@orgID, @payProfileIDList=@payProfileIDList, @lookupMode='limittocp';
			END

			INSERT INTO #tmpAuditLog (auditCode, msg)
			SELECT DISTINCT 'CP', 'Pay Profile ' + mpp.detail + ' associated to Contribution Program [' 
				+ cp.programName + ISNULL(' - ' + cpc.campaignName,'') + '] (ContributionID: ' + CAST(c.contributionID AS varchar(10)) + ')'
			FROM dbo.cp_contributionPayProfiles AS cpp 
			INNER JOIN #tmpNewCPPayProfiles as tmp on tmp.contributionPayProfileID = cpp.contributionPayProfileID
			INNER JOIN dbo.cp_contributions AS c ON c.contributionID = cpp.contributionID
			INNER JOIN dbo.cp_programs AS cp ON cp.programID = c.programID
			INNER JOIN dbo.ams_memberPaymentProfiles as mpp on mpp.payProfileID = cpp.payProfileID
			LEFT OUTER JOIN dbo.cp_campaigns AS cpc ON cpc.campaignID = c.campaignID;

			-- update non-paid invoices
			UPDATE i
			SET i.payProfileID = tmp.payProfileID,
				i.MPProfileID = tmp.MPProfileID,
				i.payProcessFee = 0,
				i.processFeePercent = NULL
				OUTPUT INSERTED.invoiceID INTO #tmpUpdatedInvoices
			FROM #tmpCPCardOnFiles as tmp
			CROSS APPLY dbo.fn_cp_contributionTransactions(tmp.contributionID) as cpt
			INNER JOIN dbo.tr_invoiceTransactions as it on it.orgID = @orgID and it.transactionID = cpt.transactionID
			INNER JOIN dbo.tr_invoices as i on i.orgID = @orgID and i.invoiceID = it.invoiceID
			INNER JOIN dbo.tr_invoiceStatuses as iStat on iStat.statusID = i.statusID
			WHERE iStat.status <> 'Paid'
			AND isnull(i.payProfileID,0) <> tmp.payProfileID;

			-- inv
			INSERT INTO #tmpAuditLog (auditCode, msg)
			SELECT 'INV', 'Pay Profile ' + mpp.detail + ' associated to Invoice ' + o.orgCode + dbo.fn_tr_padInvoiceNumber(i.invoiceNumber)
			FROM dbo.tr_invoices as i
			INNER JOIN #tmpUpdatedInvoices as tmp on tmp.invoiceID = i.invoiceID
			INNER JOIN dbo.organizations as o on o.orgID = i.orgID
			INNER JOIN dbo.ams_memberPaymentProfiles as mpp on mpp.payProfileID = i.payProfileID;

			IF EXISTS (SELECT 1 FROM #tmpAuditLog) BEGIN
				INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
				SELECT ('{ "c":"auditLog", "d": {
					"AUDITCODE":"'+ auditCode +'",
					"ORGID":' + cast(@orgID as varchar(10)) + ',
					"SITEID":' + cast(@siteID as varchar(10)) + ',
					"ACTORMEMBERID":' + cast(@recordedByMemberID as varchar(20)) + ',
					"ACTIONDATE":"' + convert(varchar(20),GETDATE(),120) + '",
					"MESSAGE":"' + replace(memberCentral.dbo.fn_cleanInvalidXMLChars(msg),'"','\"') + '" } }')
				FROM #tmpAuditLog;
			END
		COMMIT TRAN;

	END TRY
	BEGIN CATCH
		IF @@trancount > 0 ROLLBACK TRANSACTION;

		INSERT INTO #tmpCPCOFErrors (msg)
		VALUES ('Unable to associate cards on file with contributors.');

		INSERT INTO #tmpCPCOFErrors (msg)
		VALUES (left(error_message(),600));
	END CATCH

	select @importResult = (
		select getdate() as "@date",
			isnull((select top 100 PERCENT dbo.fn_RegExReplace(isnull(msg,''),'[^\x20-\x7E]','') as "@msg"
			from #tmpCPCOFErrors
			order by rowid
			FOR XML path('error'), root('errors'), type),'<errors/>')
		for xml path('import'), TYPE);

	IF OBJECT_ID('tempdb..#tmpCPCardOnFiles') IS NOT NULL 
		DROP TABLE #tmpCPCardOnFiles;
	IF OBJECT_ID('tempdb..#tmpAuditLog') IS NOT NULL 
		DROP TABLE #tmpAuditLog;
	IF OBJECT_ID('tempdb..#tmpNewCPPayProfiles') IS NOT NULL 
		DROP TABLE #tmpNewCPPayProfiles;
	IF OBJECT_ID('tempdb..#tmpUpdatedInvoices') IS NOT NULL 
		DROP TABLE #tmpUpdatedInvoices;
	IF OBJECT_ID('tempdb..#tmpChangedPayProfiles') IS NOT NULL 
		DROP TABLE #tmpChangedPayProfiles;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
