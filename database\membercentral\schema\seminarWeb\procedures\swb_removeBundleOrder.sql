ALTER PROC dbo.swb_removeBundleOrder
@orderID int,
@recordedOnSiteID int,
@recordedByMemberID int,
@statsSessionID int,
@AROption char(1)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpBundleOrderTrans') IS NOT NULL 
		DROP TABLE #tmpBundleOrderTrans;
	CREATE TABLE #tmpBundleOrderTrans (transactionID int, typeID int);

	DECLARE @depoID int, @bundleID int, @bundleName varchar(200), @message varchar(max), @currentUser varchar(126), 
		@EnrollOnOrgID int, @EnrollOnSiteID int, @EnrollOnSiteCode varchar(10), @handlesOwnPayment bit, @applicationTypeID int, 
		@minTID int, @assignedToMemberID int, @invoiceProfileID int, @invoiceID int, @GLAccountID int, @invoiceNumber varchar(18), 
		@adjAmount decimal(18,2), @registrantName varchar(300), @msgjson varchar(max), @reverseEnrollmentDepoTIDList varchar(max), 
		@enteredByDepoMemberDataID int, @nowdate datetime, @trashID int, @minEnrollmentID int, @format varchar(4), @contentID int,
		@contentAROpt char(1);
	DECLARE @tmpBundle TABLE (format varchar(4), contentID int, enrollmentID int);
	DECLARE @tblAdjust TABLE (transactionID int, amountToAdjust decimal(18,2), creditGLAccountID int);
	DECLARE @tblInvoices TABLE (invoiceID int, invoiceProfileID int);

	IF @AROption NOT IN ('A','B','C')
		GOTO on_done;

	select @nowdate = getdate();
	select @applicationTypeID = memberCentral.dbo.fn_getApplicationTypeIDFromName('SemWebCatalog');

	SELECT @bundleID = bo.bundleID, @bundleName = b.bundleName, @depoID = d.depoMemberDataID, 
		@EnrollOnSiteCode = mcs.siteCode, @EnrollOnOrgID = mcs.orgID, @EnrollOnSiteID = mcs.siteID, 
		@handlesOwnPayment = bo.handlesOwnPayment, 
		@registrantName = '[' + ISNULL(m2.firstname,d.firstName) + ' ' + ISNULL(m2.lastName,d.lastName) + ']' + ISNULL(' (' + m2.membernumber + ')','')
	FROM dbo.tblBundleOrders AS bo
	INNER JOIN dbo.tblBundles AS b ON b.bundleID = bo.bundleID
	INNER JOIN dbo.tblParticipants AS p ON p.participantID = bo.participantID
	INNER JOIN dbo.tblUsers AS u ON u.userID = bo.userID
	INNER JOIN trialsmith.dbo.depomemberdata AS d ON d.depomemberdataID = u.depoMemberDataID
	INNER JOIN memberCentral.dbo.sites as mcs on mcs.siteCode = p.orgCode
	INNER JOIN trialsmith.dbo.depoTLA AS tla ON tla.state = p.orgcode
	LEFT OUTER JOIN memberCentral.dbo.ams_members AS m 
		INNER JOIN membercentral.dbo.ams_members as m2 on m2.memberID = m.activeMemberID
	ON m.memberID = bo.MCMemberID
	WHERE bo.orderID = @orderID
	AND bo.isActive = 1;
	
	SELECT @currentUser = firstname + ' ' + lastname
	FROM membercentral.dbo.ams_members
	where memberID = @recordedByMemberID;

	SELECT @message = 'SeminarWeb Bundle Order removed from ' + @bundleName + ' by ' + @currentUser;

	EXEC memberCentral.dbo.ams_getTLASITESDepoMemberDataIDByMemberID @memberID=@recordedByMemberID, @siteID=@recordedOnSiteID, 
		@depomemberdataid=@enteredByDepoMemberDataID OUTPUT;

	-- Assocation handles payment 
	IF @handlesOwnPayment = 1 AND @AROption IN ('A','B') BEGIN
		EXEC memberCentral.dbo.ams_getMemberIDByTLASITESDepoMemberDataID @siteCode=@EnrollOnSiteCode, @depoMemberDataID=@depoID, 
			@memberID=@assignedToMemberID OUTPUT;

		-- get all bundle order transactions
		INSERT INTO #tmpBundleOrderTrans (transactionID, typeID)
		select transactionID, typeID
		from memberCentral.dbo.fn_sw_enrollmentTransactions(@orderID,'SWB')
		OPTION(RECOMPILE);

		-- put all open invoices used for enrollment into table since they were already created and can be used for adjustments
		insert into @tblInvoices (invoiceID, invoiceProfileID)
		select distinct i.invoiceID, i.invoiceProfileID
		from #tmpBundleOrderTrans as et
		inner join memberCentral.dbo.tr_invoiceTransactions as it on it.orgID = @EnrollOnOrgID and it.transactionID = et.transactionID
		inner join memberCentral.dbo.tr_invoices as i on i.orgID = @EnrollOnOrgID and i.invoiceID = it.invoiceID and i.statusID = 1;
		
		-- get all enrollment-related sales transactions we need to adjust
		IF @AROption = 'A' 
			INSERT INTO @tblAdjust (transactionID, amountToAdjust, creditGLAccountID)
			select et.transactionID, tsFull.cache_amountAfterAdjustment, t.creditGLAccountID
			from #tmpBundleOrderTrans as et
			inner join memberCentral.dbo.tr_transactions as t on t.transactionID = et.transactionID
			cross apply memberCentral.dbo.fn_tr_transactionSalesWithDIT(@EnrollOnOrgID,t.transactionID) as tsFull
			where tsFull.cache_amountAfterAdjustment > 0
			and et.typeID = 1
			OPTION(RECOMPILE);
		IF @AROption = 'B' 
			INSERT INTO @tblAdjust (transactionID, amountToAdjust, creditGLAccountID)
			select et.transactionID, tsFull.cache_amountAfterAdjustment-tsFull.cache_activePaymentAllocatedAmount-tsFull.cache_pendingPaymentAllocatedAmount, t.creditGLAccountID
			from #tmpBundleOrderTrans as et
			inner join memberCentral.dbo.tr_transactions as t on t.transactionID = et.transactionID
			cross apply memberCentral.dbo.fn_tr_transactionSalesWithDIT(@EnrollOnOrgID,t.transactionID) as tsFull
			where et.typeID = 1
			and tsFull.cache_amountAfterAdjustment-tsFull.cache_activePaymentAllocatedAmount-tsFull.cache_pendingPaymentAllocatedAmount > 0
			OPTION(RECOMPILE);
	END

	-- SW handles payment
	IF @handlesOwnPayment = 0 AND @AROption = 'A'
		SELECT @reverseEnrollmentDepoTIDList = COALESCE(@reverseEnrollmentDepoTIDList + ',', '') + cast(dt.TransactionID as varchar(10))
		FROM trialsmith.dbo.depoTransactionsApplications as dta 
		INNER JOIN trialsmith.dbo.depoTransactions as dt on dt.TransactionID = dta.transactionID
		WHERE dta.itemID = @orderID
		AND dta.itemType = 'SWBO'
		AND dt.Reversable = 'Y';

	SELECT @contentAROpt = CASE WHEN @handlesOwnPayment = 1 THEN 'C' ELSE 'B' END;

	-- get enrollments included in this bundle order
	INSERT INTO @tmpBundle (format, contentID, enrollmentID)
	SELECT CASE 
		WHEN swl.seminarid is not null THEN 'SWL' 
		WHEN swod.seminarid is not null THEN 'SWOD' 
		END AS format, e.seminarID, e.enrollmentID
	FROM dbo.tblEnrollments as e
	LEFT OUTER JOIN dbo.tblSeminarsSWOD AS swod ON swod.seminarID = e.seminarID
	LEFT OUTER JOIN dbo.tblSeminarsSWLive AS swl ON swl.seminarID = e.seminarID
	WHERE e.bundleOrderID = @orderID
	AND e.isActive = 1;

	BEGIN TRAN;
		UPDATE dbo.tblBundleOrders
		SET isActive = 0
		WHERE orderID = @orderID;

		-- remove enrollment for each item in bundle
		SELECT @minEnrollmentID = min(enrollmentID) FROM @tmpBundle;
		WHILE @minEnrollmentID IS NOT NULL BEGIN
			SELECT @format = NULL, @contentID = NULL;
			SELECT @format = format, @contentID = contentID FROM @tmpBundle WHERE enrollmentID = @minEnrollmentID;

			IF @format = 'SWOD'
				EXEC dbo.swod_removeEnrollment @enrollmentID=@minEnrollmentID, @recordedOnSiteID=@recordedOnSiteID, 
					@recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID, @AROption=@contentAROpt;
			ELSE
				EXEC dbo.swl_removeEnrollment @enrollmentID=@minEnrollmentID, @recordedOnSiteID=@recordedOnSiteID, 
					@recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID, @AROption=@contentAROpt;

			SELECT @minEnrollmentID = min(enrollmentID) FROM @tmpBundle WHERE enrollmentID > @minEnrollmentID;
		END

		-- add customer note
		INSERT INTO trialsmith.dbo.customernotes (depomemberdataid, notetypeid, note, dateentered)
		VALUES (@depoID, 1, @message, @nowdate);

		-- Assocation handles payment
		IF @handlesOwnPayment = 1 AND @AROption IN ('A','B') BEGIN
			UPDATE memberCentral.dbo.tr_applications
			SET [status] = 'D'
			WHERE itemID = @orderID
			AND itemType = 'SWBRate'
			AND applicationTypeID = @applicationTypeID
			AND [status] <> 'D';

			-- if there are adjustments to make
			IF EXISTS (select transactionID from @tblAdjust) BEGIN
				SELECT @minTID = min(transactionID) from @tblAdjust;
				WHILE @minTID IS NOT NULL BEGIN
					select @invoiceProfileID = null, @invoiceID = null, @adjAmount = null, @GLAccountID = null;

					select @adjAmount = amountToAdjust*-1, @GLAccountID = creditGLAccountID from @tblAdjust where transactionID = @minTID;
					select @invoiceProfileID = invoiceProfileID from memberCentral.dbo.tr_glAccounts where glAccountID = @GLAccountID;
					select top 1 @invoiceID = invoiceID from @tblInvoices where invoiceProfileID = @invoiceProfileID;
					
					-- if necessary, create invoice assigned to enrollment based on invoice profile
					IF @invoiceID is null BEGIN
						EXEC memberCentral.dbo.tr_createInvoice @invoiceProfileID=@invoiceProfileID, @enteredByMemberID=@recordedByMemberID, 
							@assignedToMemberID=@assignedToMemberID, @dateBilled=@nowDate, @dateDue=@nowdate, 
							@invoiceID=@invoiceID OUTPUT, @invoiceNumber=@invoiceNumber OUTPUT;

						INSERT INTO @tblInvoices (invoiceID, invoiceProfileID)
						values (@invoiceID, @invoiceProfileID);
					END	

					EXEC memberCentral.dbo.tr_createTransaction_adjustment @recordedOnSiteID=@recordedOnSiteID, @recordedByMemberID=@recordedByMemberID, 
						@statsSessionID=@statsSessionID, @amount=@adjAmount, @taxAmount=null, @transactionDate=@nowdate,
						@autoAdjustTransactionDate=1, @saleTransactionID=@minTID, @invoiceID=@invoiceID, @byPassTax=0, @byPassAccrual=0,
						@xmlSchedule=null, @transactionID=@trashID OUTPUT;
					
					SELECT @minTID = min(transactionID) from @tblAdjust where transactionID > @minTID;
				END
			END
		END

		-- SW handles payment
		IF @handlesOwnPayment = 0 AND @AROption = 'A' AND @reverseEnrollmentDepoTIDList IS NOT NULL
			EXEC trialsmith.dbo.transactions_reverse @tidList=@reverseEnrollmentDepoTIDList, @enteredByDepoMemberDataID=@enteredByDepoMemberDataID;

		EXEC dbo.sw_calculateBundleOrderRevenueAndRegFees @bundleOrderID=@orderID, @calcOnly=0, @asOfDate=@nowDate;
	COMMIT TRAN;

	-- process conditions based on seminarweb
	EXEC membercentral.dbo.ams_processSeminarWebConditionsByDepoMemberDataID @depomemberdataID=@depoID;

	SET @msgjson = 'Bundle Order cancelled for registrant '+ @registrantName +' on SWB-' + CAST(@bundleID AS varchar(10)) + '.' + char(13) + char(10)
		+ 'Accounts Receivable Option: ' + 
		CASE WHEN @handlesOwnPayment = 1 THEN
				CASE
					WHEN @AROption = 'A' THEN 'Full Refund of Entire Registration with No Balances Due'
					WHEN @AROption = 'B' THEN 'Retain Previously Applied Payments, Zero Out Remaining Balances Due on Registration'
					WHEN @AROption = 'C' THEN 'Make No Changes to Sales, Invoices and Payments Associated with this Registration'
				END
			ELSE
				CASE
					WHEN @AROption = 'A' THEN 'Full Refund of Entire Registration with No Balances Due'
					WHEN @AROption = 'B' THEN 'Make No Changes to Sales, Invoices and Payments Associated with this Registration'
				END
		END;

	INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
	VALUES ('{ "c":"auditLog", "d": {
		"AUDITCODE":"SW",
		"ORGID":' + CAST(@EnrollOnOrgID AS varchar(10)) + ',
		"SITEID":' + CAST(@EnrollOnSiteID AS varchar(10)) + ',
		"ACTORMEMBERID":' + CAST(@recordedByMemberID AS varchar(20)) + ',
		"ACTIONDATE":"' + CONVERT(varchar(20),GETDATE(),120) + '",
		"MESSAGE":"' + replace(memberCentral.dbo.fn_cleanInvalidXMLChars(@msgjson),'"','\"') + '" } }');

	on_done:
	IF OBJECT_ID('tempdb..#tmpBundleOrderTrans') IS NOT NULL
		DROP TABLE #tmpBundleOrderTrans;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
