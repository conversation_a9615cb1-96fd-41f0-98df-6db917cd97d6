ALTER PROC dbo.tr_splitAndReclassSale
@transactionID int, 
@reclassDate date,
@invoiceDueDate date,
@recordedByMemberID int,
@statsSessionID int, 
@splitXML xml,
@tids xml OUTPUT

AS
	
SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @orgID int, @t_Adjustment int, @tr_AdjustTrans int, @assignedToMemberID int, @saleDetail varchar(500),
		@parentTransactionID int, @saleStateIDForTax int, @saleZipForTax varchar(25), @saleInvoiceProfileID int, 
		@saleTransactionID int, @invoiceIDList varchar(max), @oldSaleDeferredGLAccountID int, @xmlSchedule xml,
		@deferredDateStr varchar(10), @newSaleDeferredGLAccountID int, @numRecogEntries int, @sumRecogAmts decimal(18,2),
		@paymentTransactionID int, @amountToAllocate decimal(18,2), @paymentallocatedAmount decimal(18,2),
		@MaxAmountToAllocate decimal(18,2), @rowNum int, @transType varchar(20), @recordedOnSiteID int, 
		@adjamount decimal(18,2), @adjinvoiceProfileID int, @adjtransactionDate datetime, @invoiceID int, 
		@invoiceNumber varchar(18), @salesTaxProviderName varchar(20), @bypassTaxOnSale bit, @ARGLAccountID int,
		@taxAmountOnSale decimal(18,2), @newSaleTaxJar bit, @tr_PITTaxTrans int, @origAdjTransactionID int,
		@negAdjTaxTransactionID int, @ts_active int, @taxdetail varchar(500), @taxAmt decimal(18,2), 
		@taxGLAID int, @AdjTaxTransactionID int, @taxTransactionID int, @adjSaleTransactionID int,
		@adjTransactionID int, @splitID int, @splitAmt decimal(18,2), @splitGL int, @newSaleTransactionID int,
		@merchantProfileID int, @usePayProfileID int, @invoicePendingStatusID int, @tr_DITSaleTrans int, 
		@tr_SplitSaleTrans int, @invoiceItemSiteID int, @payProcessFee bit, @processFeePercent decimal(5,2), 
		@paymentFeeTypeID tinyint;
	declare @tblSplit TABLE (splitID int IDENTITY(1,1) PRIMARY KEY, splitAmt decimal(18,2), splitGL int, scheduleID int, 
		newSaleTransactionID int);
	declare @tblTransactions TABLE (transactionID int, transType varchar(20), transactionDate datetime, 
		amount decimal(18,2), recordedOnSiteID int, invoiceID int, invoiceProfileID int, merchantProfileID int, 
		invoicePayProfileID int, payProcessFee bit, processFeePercent decimal(5,2));
	declare @tblTransactionsToAdj TABLE (transactionID int, transType varchar(20), transactionDate datetime, 
		amount decimal(18,2), recordedOnSiteID int, invoiceProfileID int, rowNum int);
	declare @tblTRApps TABLE (applicationTypeID int, transactionID int, itemType varchar(30), itemID int, subItemID int, orgID int);
	declare @tblRecog TABLE (recogDate date, recogAmt decimal(18,2), rowNum int);
	declare @tblAlloc TABLE (saleTransactionID int, paymentTransactionID int, allocatedAmount decimal(18,2));
	declare @tblInvoices TABLE (invoiceID int, invoiceProfileID int);
	declare @tblNewSales TABLE (transactionID int, glAccountID int);
	declare @tblTax TABLE (negAdjTaxTransactionID int PRIMARY KEY, taxTransactionID int, taxAmount decimal(18,2), 
		taxDetail varchar(500), GLAccountID int);


	/* ************************************** */
	/* check to ensure this is an active sale */
	/* ************************************** */
	declare @reclassAmount decimal(18,2), @earliestDate date;
	EXEC dbo.tr_getReclassAmountAndEarliestDate @transactionID=@transactionID, @reclassAmount=@reclassAmount OUTPUT, @earliestDate=@earliestDate OUTPUT;
	IF @reclassAmount = 0
		RAISERROR('Unable to reclass transactionID %d. Allowed reclass amount is $0.',16,1,@transactionID);
	IF @earliestDate > @reclassDate
		RAISERROR('Invalid reclass date. Earliest date later than reclass date',16,1);
	IF @reclassDate > getdate()
		RAISERROR('Invalid reclass date. Reclass date later than today.',16,1);

	select @orgID = t.ownedByOrgID, @assignedToMemberID = t.assignedToMemberID, @saleDetail = t.detail, 
		@parentTransactionID = t.parentTransactionID, @paymentFeeTypeID = ts.paymentFeeTypeID, 
		@saleStateIDForTax = ts.stateIDForTax, @saleZipForTax = ts.zipForTax,
		@oldSaleDeferredGLAccountID = dbo.fn_tr_getDeferredGLAccountID(t.creditGLAccountID)
	from dbo.tr_transactions as t
	inner join dbo.tr_transactionSales as ts on ts.orgID = t.ownedByOrgID and ts.transactionID = t.transactionID
	where t.transactionID = @transactionID;


	/* ************* */
	/* get constants */
	/* ************* */
	EXEC dbo.tr_getGLAccountByGLCode @orgID=@orgID, @GLCode='ACCOUNTSRECEIVABLE', @GLAccountID=@ARGLAccountID OUTPUT;
	set @t_Adjustment = dbo.fn_tr_getTypeID('Adjustment');
	set @tr_AdjustTrans = dbo.fn_tr_getRelationshipTypeID('AdjustTrans');
	set @tr_PITTaxTrans = dbo.fn_tr_getRelationshipTypeID('PITTaxTrans');
	set @tr_DITSaleTrans = dbo.fn_tr_getRelationshipTypeID('DITSaleTrans');
	set @tr_SplitSaleTrans = dbo.fn_tr_getRelationshipTypeID('SplitSaleTrans');
	set @ts_active = dbo.fn_tr_getStatusID('Active');
	select @invoicePendingStatusID = statusID from dbo.tr_invoiceStatuses where [status] = 'Pending'; 


	/* ********** */
	/* get splits */
	/* ********** */
	INSERT INTO @tblSplit (splitAmt, splitGL, scheduleID)
	select sp.s.value('@amt','decimal(18,2)'), sp.s.value('@gl','int'), sp.s.value('@sch','int')
	from @splitXML.nodes('/split/s') as sp(s);

	IF (select sum(splitAmt) from @tblSplit) <> @reclassAmount
		RAISERROR('Split amounts do not equal reclass amount.',16,1);


	/* **************** */
	/* get initial sale */
	/* **************** */
	insert into @tblTransactions (transactionID, transType, transactionDate, amount, recordedOnSiteID, 
		invoiceID, invoiceProfileID, merchantProfileID, invoicePayProfileID, payProcessFee, processFeePercent)
	select t.transactionID, 'sale', t.transactionDate, t.amount, t.recordedOnSiteID, i.invoiceID,
		i.invoiceProfileID, i.MPProfileID, i.payProfileID, i.payProcessFee, i.processFeePercent
	from dbo.tr_transactions as t
	inner join dbo.tr_invoiceTransactions as it on it.orgID = @orgID and it.transactionID = t.transactionID
	inner join dbo.tr_invoices as i on i.orgID = @orgID and i.invoiceID = it.invoiceID
	where t.ownedByOrgID = @orgID
	and t.transactionID = @transactionID;


	/* ************************** */
	/* get any active adjustments */
	/* ************************** */
	insert into @tblTransactions (transactionID, transType, transactionDate, amount, recordedOnSiteID, 
		invoiceID, invoiceProfileID, merchantProfileID, invoicePayProfileID, payProcessFee, processFeePercent)
	select tAdj.transactionID, 'posadj', tAdj.transactionDate, tAdj.amount, tAdj.recordedOnSiteID, 
		i.invoiceID, i.invoiceProfileID, i.MPProfileID, i.payProfileID, i.payProcessFee, i.processFeePercent
	from dbo.tr_transactions as tAdj
	inner join dbo.tr_relationships as trAdj on trAdj.orgID = @orgID and trAdj.typeID = @tr_AdjustTrans
		and trAdj.transactionID = tAdj.transactionID
		and trAdj.appliedToTransactionID = @transactionID
	inner join dbo.tr_invoiceTransactions as it on it.orgID = @orgID and it.transactionID = tAdj.transactionID
	inner join dbo.tr_invoices as i on i.orgID = @orgID and i.invoiceID = it.invoiceID
	where tAdj.ownedByOrgID = @orgID
	and tAdj.typeID = @t_Adjustment 
	and tAdj.statusID = 1
	and tAdj.debitGLAccountID = @ARGLAccountID;

	insert into @tblTransactions (transactionID, transType, transactionDate, amount, recordedOnSiteID, 
		invoiceID, invoiceProfileID, merchantProfileID, invoicePayProfileID, payProcessFee, processFeePercent)
	select tAdj.transactionID, 'negadj', tAdj.transactionDate, tAdj.amount, tAdj.recordedOnSiteID, 
		i.invoiceID, i.invoiceProfileID, i.MPProfileID, i.payProfileID, i.payProcessFee, i.processFeePercent
	from dbo.tr_transactions as tAdj
	inner join dbo.tr_relationships as trAdj on trAdj.orgID = @orgID and trAdj.typeID = @tr_AdjustTrans
		and trAdj.transactionID = tAdj.transactionID
		and trAdj.appliedToTransactionID = @transactionID
	inner join dbo.tr_invoiceTransactions as it on it.orgID = @orgID and it.transactionID = tAdj.transactionID
	inner join dbo.tr_invoices as i on i.orgID = @orgID and i.invoiceID = it.invoiceID
	where tAdj.ownedByOrgID = @orgID
	and tAdj.typeID = @t_Adjustment 
	and tAdj.statusID = 1
	and tAdj.creditGLAccountID = @ARGLAccountID;


	/* *********************** */
	/* get any tr_applications */
	/* *********************** */
	INSERT INTO @tblTRApps (applicationTypeID, transactionID, itemType, itemID, subItemID, orgID)
	select applicationTypeID, transactionID, itemType, itemID, subItemID, orgID
	from dbo.tr_applications
	where orgID = @orgID
	and transactionID = @transactionID
	and [status] = 'A';


	/* ***************************************************** */
	/* get card on file tied to invoices of orig transaction */
	/* ***************************************************** */
	SELECT top 1 @merchantProfileID = merchantProfileID, @usePayProfileID = invoicePayProfileID, 
		@payProcessFee = payProcessFee, @processFeePercent = processFeePercent
	from @tblTransactions
	where invoicePayProfileID is not null;


	/* ******************************** */
	/* get current recognition schedule */
	/* ******************************** */
	INSERT INTO @tblRecog (recogDate, recogAmt, rowNum)
	select recognitionDate, null, ROW_NUMBER() OVER (ORDER BY recognitionDate) as rowNum
	from (
		select dit.recognitionDate, sum(t.amount) as recogAmt
		from dbo.tr_transactions as t
		inner join dbo.tr_relationships as r on r.orgID = @orgID and r.typeID = @tr_DITSaleTrans and r.transactionID = t.transactionID
		inner join @tblTransactions as tbl on tbl.transactionID = r.appliedToTransactionID
		inner join dbo.tr_transactionDIT as dit on dit.orgID = @orgID and dit.transactionID = t.transactionID
		where t.ownedByOrgID = @orgID
		and t.statusID = 1
		and dit.isActive = 1
		group by dit.recognitionDate
	) as tmp;


	/* ******************************************* */
	/* get current allocations to sale and its adj */
	/* ******************************************* */
	INSERT INTO @tblAlloc (saleTransactionID, paymentTransactionID, allocatedAmount)
	EXEC dbo.tr_getAllocatedPaymentsofSale @orgID=@orgID, @limitToSaleTransactionID=@transactionID;


	/* *************************************************** */
	/* get transactions in order we need to deal with them */
	/* *************************************************** */
	insert into @tblTransactionsToAdj (transactionID, transType, transactionDate, amount, recordedOnSiteID, invoiceProfileID, rowNum)
	select transactionID, transType, transactionDate, amount, recordedOnSiteID, invoiceProfileID, 
		ROW_NUMBER() OVER (ORDER BY transactionDate desc) as rowNum 
	from @tblTransactions;


	/* ************************************************* */
	/* remove the rowNum for the ones we will do in mass */
	/* ************************************************* */
	UPDATE @tblTransactionsToAdj
	set rowNum = null
	where cast(transactionDate as date) <= @reclassDate;


	BEGIN TRAN;
		/* ********************************************************* */
		/* one by one do opposite of original transaction adjustment */
		/* ********************************************************* */
		select @rowNum = min(rowNum) from @tblTransactionsToAdj where rowNum is not null;
		while @rowNum is not null begin
			select @transType=null, @recordedOnSiteID=null, @adjamount=null, @adjtransactionDate=null, @adjinvoiceProfileID=null,
				@invoiceID=null, @invoiceNumber=null, @origAdjTransactionID=null;
			delete from @tblTax;
	
			select @transType=transType, @recordedOnSiteID=recordedOnSiteID, @adjamount=amount,
				@adjtransactionDate=transactionDate, @adjinvoiceProfileID=invoiceProfileID, 
				@origAdjTransactionID=transactionID
			from @tblTransactionsToAdj
			where rowNum = @rowNum;

			select @invoiceID = invoiceID from @tblInvoices where invoiceProfileID = @adjinvoiceProfileID;
			if @invoiceID is null begin
				EXEC dbo.tr_createInvoice @invoiceProfileID=@adjinvoiceProfileID, @enteredByMemberID=@recordedByMemberID, 
					@assignedToMemberID=@assignedToMemberID, @dateBilled=@invoiceDueDate, @dateDue=@invoiceDueDate, 
					@invoiceID=@invoiceID OUTPUT, @invoiceNumber=@invoiceNumber OUTPUT;

				INSERT INTO @tblInvoices (invoiceID, invoiceProfileID) 
				VALUES (@invoiceID, @adjinvoiceProfileID);
			end

			IF @transType = 'negadj' begin
				/* ******************************************* */
				/* date of pos adj is 1 second before original */
				/* ******************************************* */
				SET @adjtransactionDate = dateadd(s,-1,@adjtransactionDate);
				EXEC dbo.tr_createTransaction_adjustment @recordedOnSiteID=@recordedOnSiteID, @recordedByMemberID=@recordedByMemberID, 
					@statsSessionID=@statsSessionID, @amount=@adjamount, @taxAmount=0, @transactionDate=@adjtransactionDate, 
					@autoAdjustTransactionDate=0, @saleTransactionID=@transactionID, @invoiceID=@invoiceID, @bypassTax=1, @bypassAccrual=1, 
					@xmlSchedule=null, @transactionID=@adjTransactionID OUTPUT;

				/* ********************************************** */
				/* get neg adj to tax tied to the neg adj of sale */
				/* ********************************************** */
				insert into @tblTax (negAdjTaxTransactionID, taxTransactionID, taxAmount, taxDetail, GLAccountID)
				select tNegTax.transactionID, rTax.appliedToTransactionID, tNegTax.amount, tNegTax.detail, tNegTax.debitGLAccountID
				from dbo.tr_relationships as r
				inner join dbo.tr_transactions as tNegTax on tNegTax.ownedByOrgID = @orgID and tNegTax.transactionID = r.transactionID
				inner join dbo.tr_relationships as rTax on rTax.orgID = @orgID and rTax.typeID = @tr_AdjustTrans and rTax.transactionID = tNegTax.transactionID
				where r.orgID = @orgID 
				and r.typeID = @tr_PITTaxTrans
				and r.appliedToTransactionID = @origAdjTransactionID;

				select @negAdjTaxTransactionID = min(negAdjTaxTransactionID) from @tblTax;
				while @negAdjTaxTransactionID is not null begin
					select @taxdetail = null, @taxAmt = null, @taxGLAID = null, @AdjTaxTransactionID = null, @taxTransactionID = null;

					select @taxdetail = taxDetail, @taxAmt = taxAmount, @taxGLAID = GLAccountID, @taxTransactionID = taxTransactionID
					from @tblTax
					where negAdjTaxTransactionID = @negAdjTaxTransactionID;
					
					INSERT INTO dbo.tr_transactions (ownedByOrgID, recordedOnSiteID, statusID, detail, parentTransactionID, 
						amount, dateRecorded, transactionDate, assignedToMemberID, recordedByMemberID, statsSessionID, 
						typeID, debitGLAccountID, creditGLAccountID)
					VALUES (@orgID, @recordedOnSiteID, @ts_active, @taxdetail, null, @taxAmt, getdate(), @adjtransactionDate, 
						@assignedToMemberID, @recordedByMemberID, @statsSessionID, @t_Adjustment, @ARGLAccountID, @taxGLAID);
					select @AdjTaxTransactionID = SCOPE_IDENTITY();

					-- insert adj into relationships
					-- tie tax to the sale adjustment
					INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID, orgID)
					VALUES (@tr_AdjustTrans, @AdjTaxTransactionID, @taxTransactionID, @orgID);
					
					INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID, orgID)
					VALUES (@tr_PITTaxTrans, @AdjTaxTransactionID, @adjTransactionID, @orgID);

					-- put adj on invoice
					INSERT INTO dbo.tr_invoiceTransactions (transactionID, invoiceID, cache_invoiceAmountAfterAdjustment, cache_activePaymentAllocatedAmount, cache_pendingPaymentAllocatedAmount, orgID)
					VALUES (@AdjTaxTransactionID, @invoiceID, @taxAmt, 0, 0, @orgID);

					-- update cache
					UPDATE dbo.tr_transactionSales
					SET cache_amountAfterAdjustment = cache_amountAfterAdjustment + @taxAmt
					WHERE orgID = @orgID
					and transactionID = @taxTransactionID;

					select @negAdjTaxTransactionID = min(negAdjTaxTransactionID) from @tblTax where negAdjTaxTransactionID > @negAdjTaxTransactionID;
				end

				-- ignore accrual since it will all be adjusted to 0 anyway.

			end else begin
				/* ****************************************** */
				/* date of neg adj is 1 second after original */
				/* ****************************************** */
				SET @adjtransactionDate = dateadd(s,1,@adjtransactionDate);
				SET @adjamount = @adjamount * -1;
				EXEC dbo.tr_createTransaction_adjustment @recordedOnSiteID=@recordedOnSiteID, @recordedByMemberID=@recordedByMemberID, 
					@statsSessionID=@statsSessionID, @amount=@adjamount, @taxAmount=0, @transactionDate=@adjtransactionDate, 
					@autoAdjustTransactionDate=0, @saleTransactionID=@transactionID, @invoiceID=@invoiceID, @bypassTax=0, @bypassAccrual=0, 
					@xmlSchedule=null, @transactionID=@adjTransactionID OUTPUT;
			end

			select @rowNum = min(rowNum) from @tblTransactionsToAdj where rowNum > @rowNum;
		end


		/* ******************************************** */
		/* the rest need to be neg adj to 0 in one shot */
		/* ******************************************** */
		select @adjamount=null, @adjtransactionDate=null, @adjinvoiceProfileID=null, @invoiceID=null, 
			@invoiceNumber=null, @recordedOnSiteID=null, @adjTransactionID = null;

		select @adjamount = sum(amount) * -1 from @tblTransactionsToAdj where rowNum is null;
		set @adjtransactionDate = dateadd(s,-1,dateadd(day,1,DATEADD(dd, DATEDIFF(dd,0,@reclassDate), 0)));
		select @adjinvoiceProfileID = invoiceProfileID, @recordedOnSiteID = recordedOnSiteID from @tblTransactionsToAdj where transType = 'sale';

		select @invoiceID = invoiceID from @tblInvoices where invoiceProfileID = @adjinvoiceProfileID;
		if @invoiceID is null begin
			EXEC dbo.tr_createInvoice @invoiceProfileID=@adjinvoiceProfileID, @enteredByMemberID=@recordedByMemberID, 
				@assignedToMemberID=@assignedToMemberID, @dateBilled=@invoiceDueDate, @dateDue=@invoiceDueDate, 
				@invoiceID=@invoiceID OUTPUT, @invoiceNumber=@invoiceNumber OUTPUT;

			INSERT INTO @tblInvoices (invoiceID, invoiceProfileID) 
			VALUES (@invoiceID, @adjinvoiceProfileID);
		end

		EXEC dbo.tr_createTransaction_adjustment @recordedOnSiteID=@recordedOnSiteID, @recordedByMemberID=@recordedByMemberID, 
			@statsSessionID=@statsSessionID, @amount=@adjamount, @taxAmount=0, @transactionDate=@adjtransactionDate, 
			@autoAdjustTransactionDate=0, @saleTransactionID=@transactionID, @invoiceID=@invoiceID, @bypassTax=0, @bypassAccrual=0, 
			@xmlSchedule=null, @transactionID=@adjTransactionID OUTPUT;


		/* ******************************************** */
		/* create new sale for each reclass amount      */
		/* If new GL is deferred and old was deferred, keep original schedule and spread across splits by percentage. */
		/* If new GL is deferred and old was not deferred, create new schedule with one date and amount */
		/* ******************************************** */
		select @splitID = min(splitID) from @tblSplit;
		while @splitID is not null begin
			select @splitAmt = null, @splitGL = null, @saleInvoiceProfileID=null, @invoiceID=null, 
				@invoiceNumber=null, @saleTransactionID = null, @xmlSchedule = null, 
				@newSaleDeferredGLAccountID = null;

			select @splitAmt = splitAmt, @splitGL = splitGL
			from @tblSplit
			where splitID = @splitID;

			select @saleInvoiceProfileID = invoiceProfileID from dbo.tr_glAccounts where glAccountID = @splitGL;
			select @newSaleDeferredGLAccountID = dbo.fn_tr_getDeferredGLAccountID(@splitGL);

			select @invoiceID = invoiceID from @tblInvoices where invoiceProfileID = @saleInvoiceProfileID;
			if @invoiceID is null begin
				EXEC dbo.tr_createInvoice @invoiceProfileID=@saleInvoiceProfileID, @enteredByMemberID=@recordedByMemberID, 
					@assignedToMemberID=@assignedToMemberID, @dateBilled=@invoiceDueDate, @dateDue=@invoiceDueDate, 
					@invoiceID=@invoiceID OUTPUT, @invoiceNumber=@invoiceNumber OUTPUT;

				INSERT INTO @tblInvoices (invoiceID, invoiceProfileID) 
				VALUES (@invoiceID, @saleInvoiceProfileID);
			end

			if @newSaleDeferredGLAccountID is null
				set @xmlSchedule = null;
			else begin
				if @oldSaleDeferredGLAccountID is null begin
					set @deferredDateStr = convert(varchar(10),@reclassDate,101);
					set @xmlSchedule = '<rows><row amt="' + cast(@splitAmt as varchar(20)) + '" dt="' + @deferredDateStr + '" /></rows>';
				end else begin
					select @numRecogEntries = count(*) from @tblRecog;

					-- spread amt over all dates
					update @tblRecog
					set recogAmt = cast(@splitAmt / cast(@numRecogEntries as decimal(18,2)) as decimal(18,2));

					-- check for remainders and adjust last entry if any
					select @sumRecogAmts = sum(recogAmt) from @tblRecog;
					IF @sumRecogAmts <> @splitAmt
						update @tblRecog
						set recogAmt = recogAmt + (@splitAmt-@sumRecogAmts)
						where rowNum = @numRecogEntries;

					-- check for remainders again and error
					select @sumRecogAmts = sum(recogAmt) from @tblRecog;
					IF @sumRecogAmts <> @splitAmt
						RAISERROR('Unable to split deferred schedule.',16,1);

					SELECT @xmlSchedule = (
						select row.recogAmt as amt, convert(varchar(10),row.recogDate,101) as dt
						from @tblRecog as row
						for XML AUTO, ROOT('rows'), TYPE
					);
				end
			end

			/* ******************************************** */
			/* see if the new sale GL uses TaxJar.          */
			/* if taxjar toggle @bypassTax to 1 and @taxAmount = 0, and record alert here */
			/* ******************************************** */
			select @salesTaxProviderName=stpr.providerName
			from dbo.tr_glAccounts as gl
			inner join dbo.tr_salesTaxProfiles as stp on stp.profileID = gl.salesTaxProfileID
			inner join dbo.tr_salesTaxProviders as stpr on stpr.providerID = stp.providerID
			where gl.glAccountID = @splitGL
			and stp.status = 'A';

			IF @salesTaxProviderName = 'TaxJar' BEGIN
				SET @bypassTaxOnSale = 1;
				SET @taxAmountOnSale = 0;
				SET @newSaleTaxJar = 1;
			END ELSE BEGIN
				SET @bypassTaxOnSale = 0;
				SET @taxAmountOnSale = null;
				SET @newSaleTaxJar = 0;
			END

			/* *************** */
			/* record new sale */
			/* *************** */
			EXEC dbo.tr_createTransaction_sale @ownedByOrgID=@orgID, @recordedOnSiteID=@recordedOnSiteID, @assignedToMemberID=@assignedToMemberID, 
				@recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID, @status='Active', @detail=@saleDetail, 
				@parentTransactionID=@parentTransactionID, @amount=@splitAmt, @transactionDate=@adjtransactionDate, 
				@creditGLAccountID=@splitGL, @invoiceID=@invoiceID, @stateIDForTax=@saleStateIDForTax, @zipForTax=@saleZipForTax, 
				@taxAmount=@taxAmountOnSale, @bypassTax=@bypassTaxOnSale, @bypassInvoiceMessage=1, @bypassAccrual=0, @xmlSchedule=@xmlSchedule, 
				@transactionID=@saleTransactionID OUTPUT;

			INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID, orgID)
			VALUES (@tr_SplitSaleTrans, @saleTransactionID, @transactionID, @orgID);

			INSERT INTO @tblNewSales (transactionID, glAccountID) VALUES (@saleTransactionID, @splitGL);

			update @tblSplit
			set newSaleTransactionID = @saleTransactionID
			where splitID = @splitID;

			IF @newSaleTaxJar = 1
				INSERT INTO dbo.tr_transactionAlerts (orgID, memberID, transactionID, [message])
				VALUES (@orgID, @assignedToMemberID, @saleTransactionID, 'Unable to obtain or record sales tax for this sale. Reclassing sales do not support TaxJar sales tax.');
	
			if @newSaleDeferredGLAccountID is not null and @oldSaleDeferredGLAccountID is null
				INSERT INTO dbo.tr_transactionAlerts (orgID, memberID, transactionID, [message])
				VALUES (@orgID, @assignedToMemberID, @saleTransactionID, 'Unable to create deferral recognition schedule for this sale. You may need to adjust the recognition schedule manually.');

			select @splitID = min(splitID) from @tblSplit where splitID > @splitID;
		end

		/* ******************************************* */
		/* associate paymentFeeTypeID to the new sales */
		/* ******************************************* */
		IF @paymentFeeTypeID IS NOT NULL
			update ts
			set ts.paymentFeeTypeID = @paymentFeeTypeID
			from dbo.tr_transactionSales as ts
			inner join @tblNewSales as tmp on tmp.transactionID = ts.transactionID
			where ts.orgID = @orgID;

		/* ********************************* */
		/* tie new sales to defined schedule */
		/* ********************************* */
		INSERT INTO dbo.tr_transactionLimitSchedules (transactionID, scheduleID, orgID)
		select newSaleTransactionID, scheduleID, @orgID
		from @tblSplit
		where newSaleTransactionID is not null
		and scheduleID is not null;

		
		/* *********************************************************** */
		/* associate previous invoice card on file to the new invoices */
		/* *********************************************************** */
		IF @usePayProfileID is not null BEGIN
			update i
			set i.payProfileID = @usePayProfileID,
				i.MPProfileID = @merchantProfileID,
				i.payProcessFee = @payProcessFee,
				i.processFeePercent = @processFeePercent
			from dbo.tr_invoices as i
			inner join @tblInvoices as tmp on tmp.invoiceID = i.invoiceID
			where i.orgID = @orgID;

			INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
			SELECT '{ "c":"auditLog", "d": {
				"AUDITCODE":"INV",
				"ORGID":' + cast(s.orgID as varchar(10)) + ',
				"SITEID":' + cast(s.siteID as varchar(10)) + ',
				"ACTORMEMBERID":' + cast(@recordedByMemberID as varchar(20)) + ',
				"ACTIONDATE":"' + convert(varchar(20),GETDATE(),120) + '",
				"MESSAGE":"' + replace(memberCentral.dbo.fn_cleanInvalidXMLChars('Pay Profile ' + mpp.detail + ' associated to Invoice ' + o.orgCode + dbo.fn_tr_padInvoiceNumber(i.invoiceNumber)),'"','\"') + '" } }'
			FROM dbo.tr_invoices as i
			INNER JOIN @tblInvoices as tmp on tmp.invoiceID = i.invoiceID
			INNER JOIN dbo.organizations as o on o.orgID = i.orgID
			INNER JOIN dbo.ams_memberPaymentProfiles as mpp on mpp.payProfileID = i.payProfileID
			INNER JOIN dbo.mp_profiles AS mp ON mp.profileID = i.MPProfileID
			INNER JOIN dbo.sites AS s ON s.siteID = mp.siteID;
		END


		/* ******************************************************************** */
		/* close all invoices. Previous pending invoices should also be closed. */
		/* ******************************************************************** */
		insert into @tblInvoices (invoiceID)
		select i.invoiceID
		from @tblTransactions as t
		inner join dbo.tr_invoices as i on i.orgID = @orgID and i.invoiceID = t.invoiceID
			and i.statusID = @invoicePendingStatusID;

		select @invoiceIDList = COALESCE(@invoiceIDList+',','') + cast(invoiceID as varchar(10)) from @tblInvoices;
		EXEC dbo.tr_closeInvoice @orgID=@orgID, @enteredByMemberID=@recordedByMemberID, @invoiceIDList=@invoiceIDList;


		/* ********************************************* */
		/* associate tr_application entries to new sales */
		/* ********************************************* */
		INSERT INTO dbo.tr_applications (applicationTypeID, transactionID, itemType, itemID, subItemID, [status], orgID)
		select tra.applicationTypeID, ns.transactionID, tra.itemType, tra.itemID, tra.subItemID, 'A', tra.orgID
		from @tblTRApps as tra
		cross apply @tblNewSales as ns;

		UPDATE dbo.tr_applications
		SET [status] = 'D'
		where transactionID = @transactionID
		and [status] <> 'D';
		
		
		/* *********************** */
		/* reallocate to new sales */
		/* *********************** */
		set @splitID = null;
		select @splitID = min(splitID) from @tblSplit;
		while @splitID is not null begin
			select @splitAmt = null, @splitGL = null, @MaxAmountToAllocate = null, @paymentTransactionID = null, @newSaleTransactionID = null;

			select @splitAmt = splitAmt, @splitGL = splitGL, @newSaleTransactionID = newSaleTransactionID
			from @tblSplit
			where splitID = @splitID;
		
			set @MaxAmountToAllocate = @splitAmt;
			select @paymentTransactionID = min(paymentTransactionID) from @tblAlloc where allocatedAmount > 0;
			while @paymentTransactionID is not null begin
				set @paymentallocatedAmount = null;
				select @paymentallocatedAmount = allocatedAmount from @tblAlloc where paymentTransactionID = @paymentTransactionID;
		
				IF @paymentallocatedAmount <= @MaxAmountToAllocate
					SET @amountToAllocate = @paymentallocatedAmount;
				IF @paymentallocatedAmount > @MaxAmountToAllocate
					SET @amountToAllocate = @MaxAmountToAllocate;
				IF @amountToAllocate < 0
					SET @amountToAllocate = 0;

				IF @amountToAllocate > 0
					EXEC dbo.tr_allocateToSale @recordedOnSiteID=@recordedOnSiteID, @recordedByMemberID=@recordedByMemberID, 
						@statsSessionID=@statsSessionID, @amount=@amountToAllocate, @transactionDate=@adjtransactionDate, 
						@paymentTransactionID=@paymentTransactionID, @saleTransactionID=@newSaleTransactionID;

				update @tblAlloc
				set allocatedAmount = allocatedAmount - @amountToAllocate
				where paymentTransactionID = @paymentTransactionID;

				SET @MaxAmountToAllocate = @MaxAmountToAllocate - @amountToAllocate;
				IF @MaxAmountToAllocate <= 0
					break;

				select @paymentTransactionID = min(paymentTransactionID) from @tblAlloc where allocatedAmount > 0 and paymentTransactionID > @paymentTransactionID;
			end

			select @splitID = min(splitID) from @tblSplit where splitID > @splitID;
		end

		/* ********************** */
		/* populate invoice items */
		/* ********************** */
		SELECT TOP 1 @invoiceItemSiteID = tri.siteID
		FROM dbo.tr_invoiceItems AS tri 
		INNER JOIN @tblInvoices AS tmp ON tmp.invoiceID = tri.invoiceID
		WHERE tri.orgID = @orgID;

		IF @invoiceItemSiteID IS NOT NULL
			EXEC dbo.tr_populateInvoiceItems @siteID=@invoiceItemSiteID, @invoiceIDList=@invoiceIDList;
	COMMIT TRAN;

	select @tids = (select transactionID as tid, glAccountID as gl from @tblNewSales FOR XML RAW('t'), root('tr'));

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
END CATCH
GO
