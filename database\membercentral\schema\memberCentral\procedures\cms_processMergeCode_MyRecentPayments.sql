ALTER PROC dbo.cms_processMergeCode_MyRecentPayments
@maxRows int,
@memberID int,
@siteID int,
@orgID int,
@orgcode varchar(10),
@paymentProfile varchar(1000)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	declare @statusID int, @tr_AdjustTrans int, @tr_SalesTaxTrans int, @tr_DITSaleTrans int;
	select @statusID = statusID from dbo.tr_statuses where [status] = 'Active';
	set @tr_AdjustTrans = dbo.fn_tr_getRelationshipTypeID('AdjustTrans');
	set @tr_SalesTaxTrans = dbo.fn_tr_getRelationshipTypeID('SalesTaxTrans');
	set @tr_DITSaleTrans = dbo.fn_tr_getRelationshipTypeID('DITSaleTrans');

	IF OBJECT_ID('tempdb..#tmpPayments') IS NOT NULL
		DROP TABLE #tmpPayments;
	IF OBJECT_ID('tempdb..#tmpAlloc') IS NOT NULL
		DROP TABLE #tmpAlloc;
	IF OBJECT_ID('tempdb..#tmpTrans') IS NOT NULL
		DROP TABLE #tmpTrans;
	IF OBJECT_ID('tempdb..#tmpPaymentProfile') IS NOT NULL
		DROP TABLE #tmpPaymentProfile;
	CREATE TABLE #tmpPayments (transactionID int PRIMARY KEY, transactionDate datetime, amount decimal(18,2), detail varchar(500));
	CREATE TABLE #tmpAlloc (paymentTransactionID int, allocAmount decimal(18,2), amount decimal(18,2), allocDate datetime, forAllocTypeID int, 
		forAllocTransactionID int, transactionID int);
	CREATE TABLE #tmpTrans (paymentTransactionID int, transactionID int);
	CREATE TABLE #tmpPaymentProfile (profileID int PRIMARY KEY);
	
	-- status of mp_profile doesnt matter here.
	IF len(@paymentProfile) > 0
		INSERT INTO #tmpPaymentProfile (profileID) 
		SELECT distinct mp.profileID
		FROM dbo.mp_profiles as mp
		inner join dbo.fn_VarCharListToTable(@paymentProfile,'|') as li on li.listItem = mp.profileCode
		where mp.siteID = @siteID;
	ELSE
		INSERT INTO #tmpPaymentProfile (profileID) 
		SELECT profileID 
		FROM dbo.mp_profiles
		where siteID = @siteID;

	-- get last payments
	insert into #tmpPayments (transactionID, transactionDate, amount, detail)
	select top (@maxRows) t.transactionID, t.transactionDate, t.amount, t.detail
	from dbo.tr_transactions as t
	inner join dbo.tr_transactionPayments as tp on tp.orgID = @orgID
		and tp.transactionID = t.transactionID
	inner join #tmpPaymentProfile as tpp ON tpp.profileID = tp.profileID
	inner join dbo.ams_members as m on m.orgID = @orgID
		and m.memberID = t.assignedToMemberID
		and m.activeMemberID = @memberID
	where t.ownedByOrgID = @orgID
	and t.recordedOnSiteID = @siteID
	and t.typeID = 2
	and t.statusID = @statusID
	order by t.transactionDate desc;
			 
	-- current allocations (show rolled up to sales -- tax/adj/dit included)
	insert into #tmpAlloc (paymentTransactionID, allocAmount, amount, allocDate, forAllocTypeID, forAllocTransactionID)
	select tmpP.transactionID as paymentTransactionID, atop.allocAmount, t.amount, atop.allocDate, t.typeID, atop.transactionID
	from #tmpPayments as tmpP
	cross apply dbo.fn_tr_getAllocatedTransactionsofPayment(@orgID,tmpP.transactionID) as atop
	inner join dbo.tr_transactions as t on t.ownedByOrgID = @orgID 
		and t.recordedOnSiteID = @siteID
		and t.transactionID = atop.transactionID;

	update #tmpAlloc set transactionID = forAllocTransactionID where forAllocTypeID = 1;

	update tmp
	set tmp.transactionID = 
		case
		when tAdjee.typeID = 1 then tAdjee.transactionID
		when tAdjee.typeID = 7 then rAdjTax.AppliedToTransactionID
		end
	from #tmpAlloc as tmp
	inner join dbo.tr_relationships as rAdj on rAdj.orgID = @orgID and rAdj.typeID = @tr_AdjustTrans and rAdj.transactionID = tmp.forAllocTransactionID
	inner join dbo.tr_transactions as tAdjee on tAdjee.ownedByOrgID = @orgID and tAdjee.recordedOnSiteID = @siteID and tAdjee.transactionID = rAdj.AppliedToTransactionID
	left outer join dbo.tr_relationships as rAdjTax on rAdjTax.orgID = @orgID and rAdjTax.typeID = @tr_SalesTaxTrans and rAdjTax.transactionID = tAdjee.transactionID
	where tmp.forAllocTypeID = 3;

	update tmp
	set tmp.transactionID = rTax.AppliedToTransactionID
	from #tmpAlloc as tmp
	inner join dbo.tr_relationships as rTax on rTax.orgID = @orgID and rTax.typeID = @tr_SalesTaxTrans and rTax.transactionID = tmp.forAllocTransactionID
	where tmp.forAllocTypeID = 7;

	update tmp
	set tmp.transactionID = 
		case
		when tDit.typeID = 1 then tDit.transactionID
		when tDit.typeID = 7 then rDitTax.AppliedToTransactionID
		when tDit.typeID = 3 and tDitAdjee.typeID = 1 then tDitAdjee.transactionID
		when tDit.typeID = 3 and tDitAdjee.typeID = 7 then rDitAdjTax.AppliedToTransactionID
		end
	from #tmpAlloc as tmp
	inner join dbo.tr_relationships as rDit on rDit.orgID = @orgID and rDit.typeID = @tr_DITSaleTrans 
		and rDit.transactionID = tmp.forAllocTransactionID
	inner join dbo.tr_transactions as tDit on tDit.ownedByOrgID = @orgID and tDit.recordedOnSiteID = @siteID and tDit.transactionID = rDit.AppliedToTransactionID
	left outer join dbo.tr_relationships as rDitTax on rDitTax.orgID = @orgID and rDitTax.typeID = @tr_SalesTaxTrans and rDitTax.transactionID = tDit.transactionID
	left outer join dbo.tr_relationships as rDitAdj 
		inner join dbo.tr_transactions as tDitAdjee on tDitAdjee.ownedByOrgID = @orgID and tDitAdjee.transactionID = rDitAdj.AppliedToTransactionID
		left outer join dbo.tr_relationships as rDitAdjTax on rDitAdjTax.orgID = @orgID and rDitAdjTax.typeID = @tr_SalesTaxTrans and rDitAdjTax.transactionID = tDitAdjee.transactionID
		on rDitAdj.typeID = @tr_AdjustTrans 
		and rDitAdj.orgID = @orgID and rDitAdj.transactionID = tDit.transactionID
	where tmp.forAllocTypeID = 10;

	insert into #tmpTrans (paymentTransactionID, transactionID)
	select distinct tmp.paymentTransactionID, tmp.transactionID
	from #tmpAlloc as tmp;
			 
	-- return the payments and invoices
	select distinct tmpP.transactionID, tmpP.transactionDate, tmpP.amount, tmpP.detail, i.invoiceID,
		@orgCode + dbo.fn_tr_padInvoiceNumber(i.invoiceNumber) as invoiceNumber, i.dateDue, i.invoiceCode
	from #tmpPayments as tmpP
	inner join #tmpTrans as tt on tt.paymentTransactionID = tmpP.transactionID
	inner join dbo.tr_invoiceTransactions it on it.orgID = @orgID and it.transactionID = tt.transactionID
	inner join dbo.tr_invoices as i on i.orgID = @orgID and i.invoiceID = it.invoiceID
	order by tmpP.transactionDate desc, invoiceNumber desc;

	IF OBJECT_ID('tempdb..#tmpPayments') IS NOT NULL
		DROP TABLE #tmpPayments;
	IF OBJECT_ID('tempdb..#tmpAlloc') IS NOT NULL
		DROP TABLE #tmpAlloc;
	IF OBJECT_ID('tempdb..#tmpTrans') IS NOT NULL
		DROP TABLE #tmpTrans;
	IF OBJECT_ID('tempdb..#tmpPaymentProfile') IS NOT NULL
		DROP TABLE #tmpPaymentProfile;

	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
