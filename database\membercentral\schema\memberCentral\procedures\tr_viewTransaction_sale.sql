ALTER PROC dbo.tr_viewTransaction_sale
@transactionID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @orgID int, @tr_AdjustTrans int, @tr_DITSaleTrans int, @tr_SalesTaxTrans int, @tr_WriteOffSaleTrans int;
	declare @allGLs TABLE (GLAccountID int, thePathExpanded varchar(max), accountCode varchar(200), accountType varchar(30), G<PERSON><PERSON> varchar(30), thePath varchar(max));
	declare @tblHold TABLE (transactionID int, debitglAccountID int, creditglAccountID int, amount decimal(18,2));
	declare @tblTrans TABLE (transactionID int, glAccountID int, debitAmount decimal(18,2), creditAmount decimal(18,2));
	declare @tblAlloc TABLE (saleTransactionID int, paymentTransactionID int, allocatedAmount decimal(18,2));

	select @orgID = ownedByOrgID from dbo.tr_transactions where transactionID = @transactionID;
	set @tr_AdjustTrans = dbo.fn_tr_getRelationshipTypeID('AdjustTrans');
	set @tr_DITSaleTrans = dbo.fn_tr_getRelationshipTypeID('DITSaleTrans');
	set @tr_SalesTaxTrans = dbo.fn_tr_getRelationshipTypeID('SalesTaxTrans');
	set @tr_WriteOffSaleTrans = dbo.fn_tr_getRelationshipTypeID('WriteOffSaleTrans');

	insert into @allGLS
	select rgl.GLAccountID, rgl.thePathExpanded, rgl.accountCode, rgl.accountType, rgl.GLCode, rgl.thePath
	from dbo.fn_getRecursiveGLAccountsWithAccountTypes(@orgID) as rgl;

	-- transaction info
	select TOP 1 t.transactionid, t.ownedByOrgID, t.recordedOnSiteID, 'Sale' as [type], t.detail, t.amount, 
		t.transactionDate, t.dateRecorded, ts.status, mAss2.memberID as assignedTomemberID, 
		mAss2.firstname + isnull(' ' + nullif(mAss2.middlename,''),'') + ' ' + mAss2.lastname + isnull(' ' + nullif(mAss2.suffix,''),'') + ' (' + mAss2.membernumber + ')' as assignedToMember,
		mAss2.company as assignedToMemberCompany,
		m2.firstname + isnull(' ' + nullif(m2.middlename,''),'') + ' ' + m2.lastname + isnull(' ' + nullif(m2.suffix,''),'') + ' (' + m2.membernumber + ')' as recordedByMember,
		m2.company as recordedByMemberCompany
	from dbo.tr_transactions as t
	inner join dbo.tr_statuses as ts on ts.statusID = t.statusID
	inner join dbo.ams_members as mAss on mAss.orgID = @orgID and mAss.memberid = t.assignedToMemberID
	inner join dbo.ams_members as mAss2 on mAss2.orgID = @orgID and mAss2.memberID = mAss.activeMemberID
	inner join dbo.ams_members as m on m.memberid = t.recordedByMemberID
	inner join dbo.ams_members as m2 on m2.memberID = m.activeMemberID
	where t.ownedByOrgID = @orgID 
	and t.transactionID = @transactionID;

	-- sale info
	select top 1 ts.saleID, 
		tsFull.cache_amountAfterAdjustment-tsFull.cache_activePaymentAllocatedAmount-tsFull.cache_pendingPaymentAllocatedAmount as unAllocatedAmount,
		tsFull.cache_activePaymentAllocatedAmount+tsFull.cache_pendingPaymentAllocatedAmount as allocatedAmount,
		paymentDueAmount =
			case 
			when t.statusID = 1 then (isnull(tax.cache_amountAfterAdjustment,0)+tsFull.cache_amountAfterAdjustment) - (isnull(tax.cache_activePaymentAllocatedAmount,0)+tsFull.cache_activePaymentAllocatedAmount) - (isnull(tax.cache_pendingPaymentAllocatedAmount,0)+tsFull.cache_pendingPaymentAllocatedAmount)
			else 0
			end,
		btn_canApplyPayment = 
			case 
			when t.statusID = 1 and ts.paymentFeeTypeID is null and (isnull(tax.cache_amountAfterAdjustment,0)+tsFull.cache_amountAfterAdjustment) - (isnull(tax.cache_activePaymentAllocatedAmount,0)+tsFull.cache_activePaymentAllocatedAmount) > 0 then 1 
			else 0
			end,
		btn_canWriteOff =
			case 
			when t.statusID = 1 and ts.paymentFeeTypeID is null and tsFull.cache_amountAfterAdjustment-tsFull.cache_activePaymentAllocatedAmount > 0 then 1 
			else 0 
			end,
		btn_canAdjust = case when t.statusID = 1 and ts.paymentFeeTypeID is null then 1 else 0 end,
		btn_canVoid = case when t.statusID = 1 and ts.paymentFeeTypeID is null then 1 else 0 end,
		hasAdjustments = case when exists (
			select adj.transactionID
			from dbo.tr_transactions as adj
			inner join dbo.tr_relationships as rInner on rInner.orgID = @orgID and rInner.typeID = @tr_AdjustTrans and rInner.transactionID = adj.transactionID and rInner.appliedToTransactionID = t.transactionID
			where adj.ownedByOrgID = @orgID
			and adj.statusID = 1
			) then 1 else 0 end,
		hasSchedule = case when exists (
			select dit.transactionID
			from dbo.tr_transactions as dit
			inner join dbo.tr_relationships as rInner on rInner.orgID = @orgID and rInner.typeID = @tr_DITSaleTrans and rInner.transactionID = dit.transactionID and rInner.appliedToTransactionID = t.transactionID
			inner join dbo.tr_transactionDIT as tdit on tdit.orgID = @orgID and tdit.transactionID = dit.transactionID
			where dit.ownedByOrgID = @orgID 
			and dit.statusID = 1
			and tdit.isActive = 1
			) then 1 else 0 end
	from dbo.tr_transactionSales as ts
	inner join dbo.tr_transactions as t on t.ownedByOrgID = @orgID and t.transactionID = ts.transactionID
	cross apply dbo.fn_tr_transactionSalesWithDIT(@orgID,t.transactionID) as tsFull
	outer apply (
		select sum(tsTaxFull.cache_amountAfterAdjustment) as cache_amountAfterAdjustment, 
			   sum(tsTaxFull.cache_activePaymentAllocatedAmount) as cache_activePaymentAllocatedAmount, 
			   sum(tsTaxFull.cache_pendingPaymentAllocatedAmount) as cache_pendingPaymentAllocatedAmount
		from dbo.tr_relationships as tr 
		inner join dbo.tr_transactions as tTax on tTax.ownedByOrgID = @orgID and tr.typeID = @tr_SalesTaxTrans and tTax.transactionID = tr.transactionID and tTax.statusID = 1
		cross apply dbo.fn_tr_transactionSalesWithDIT(@orgID,tTax.transactionID) as tsTaxFull
		where tr.orgID = @orgID
		and tr.appliedToTransactionID = t.transactionID
		) as tax
	where ts.orgID = @orgID
	and ts.transactionID = @transactionID;

	-- invoices
	select distinct i.invoiceID, ins.status, o.orgcode + dbo.fn_tr_padInvoiceNumber(i.invoiceNumber) as invoiceNumber, i.dateDue, i.invoiceProfileID, ip.profileName, i.invoiceCode
	from dbo.tr_transactions as t
	inner join dbo.tr_invoiceTransactions as it on it.orgID = @orgID and it.transactionID = t.transactionID
	inner join dbo.tr_invoices as i on i.orgID = @orgID and i.invoiceID = it.invoiceID
	inner join dbo.tr_invoiceStatuses as ins on ins.statusID = i.statusID
	inner join dbo.tr_invoiceProfiles as ip on ip.profileID = i.invoiceProfileID
	inner join dbo.organizations as o on o.orgID = @orgID
	where t.ownedByOrgID = @orgID
	and t.transactionID in (
		select @transactionID as transactionID
		union
		select adj.transactionID
		from dbo.tr_transactions as adj
		inner join dbo.tr_relationships as rInner on rInner.orgID = @orgID and rInner.typeID = @tr_AdjustTrans and rInner.transactionID = adj.transactionID
		where adj.ownedByOrgId = @orgID
		and rInner.appliedToTransactionID = @transactionID
	)
	order by i.dateDue desc, 3 desc;
	
	-- current allocations (also include writeoffs)
	INSERT INTO @tblAlloc (saleTransactionID, paymentTransactionID, allocatedAmount)
	EXEC dbo.tr_getAllocatedPaymentsofSale @orgID=@orgID, @limitToSaleTransactionID=@transactionID;

	select t.transactionID, t.typeID, apos.allocatedAmount as allocAmount, t.amount, t.detail, t.transactionDate, 
		mAss2.firstname + isnull(' ' + nullif(mAss2.middlename,''),'') + ' ' + mAss2.lastname + isnull(' ' + nullif(mAss2.suffix,''),'') + ' (' + mAss2.membernumber + ')' as assignedToMember,
		mAss2.company as assignedToMemberCompany,
		glDeb.thePathExpanded + isnull(' (' + nullIf(glDeb.accountCode,'') + ')','') as debitGL
	from @tblAlloc as apos
	inner join dbo.tr_transactions as t on t.ownedByOrgID = @orgID and t.transactionID = apos.paymentTransactionID
	inner join dbo.ams_members as mAss on mAss.orgID = @orgID and mAss.memberid = t.assignedToMemberID
	inner join dbo.ams_members as mAss2 on mAss2.orgID = @orgID and mAss2.memberID = mAss.activeMemberID
	inner join @allGLS as glDeb on glDeb.GLAccountID = t.debitGLAccountID
		union all
	select tWO.transactionID, tWO.typeID, tWO.amount, tWO.amount, 'WriteOff of ' + tWO.detail, tWO.transactionDate, null, null, 
		glDeb.thePathExpanded + isnull(' (' + nullIf(glDeb.accountCode,'') + ')','') as debitGL
	from dbo.tr_transactions as tWO
	inner join dbo.tr_relationships as rInner on rInner.orgID = @orgID and rInner.typeID = @tr_WriteOffSaleTrans and rInner.transactionID = tWO.transactionID
	inner join dbo.ams_members as mAss on mAss.orgID = @orgID and mAss.memberid = tWO.assignedToMemberID
	inner join dbo.ams_members as mAss2 on mAss2.orgID = @orgID and mAss2.memberID = mAss.activeMemberID
	inner join @allGLS as glDeb on glDeb.GLAccountID = tWO.debitGLAccountID
	where tWO.ownedByOrgID = @orgID 
	and rInner.appliedToTransactionID = @transactionID
	and tWO.statusID = 1
		union all
	select tWO.transactionID, tWO.typeID, tWO.amount, tWO.amount, 'WriteOff of ' + tWO.detail, tWO.transactionDate, null, null, 
		glDeb.thePathExpanded + isnull(' (' + nullIf(glDeb.accountCode,'') + ')','') as debitGL
	from dbo.tr_transactions as tWO
	inner join dbo.tr_relationships as rWO on rWO.orgID = @orgId and rWO.typeID = @tr_WriteOffSaleTrans and rWO.transactionID = tWO.transactionID
	inner join dbo.tr_transactions as tAdj on tAdj.ownedByOrgID = @orgID and tAdj.transactionID = rWO.appliedToTransactionID and tAdj.typeID = 3
	inner join dbo.tr_relationships as rAdj on rAdj.orgID = @orgID and rAdj.typeID = @tr_AdjustTrans and rAdj.transactionID = tAdj.transactionID
	inner join dbo.ams_members as mAss on mAss.orgID = @orgID and mAss.memberid = tWO.assignedToMemberID
	inner join dbo.ams_members as mAss2 on mAss2.orgID = @orgID and mAss2.memberID = mAss.activeMemberID
	inner join @allGLS as glDeb on glDeb.GLAccountID = tWO.debitGLAccountID
	where tWO.ownedByOrgID = @orgID 
	and rAdj.appliedToTransactionID = @transactionID
	and tWO.statusID = 1
	order by 6 desc;

	-- current gl spread

	-- sale	
	INSERT INTO @tblHold
	select transactionID, debitglAccountID, creditglAccountID, amount
	from dbo.tr_transactions
	WHERE transactionID = @transactionID
	and statusID = 1;

	-- adj
	INSERT INTO @tblHold
	select distinct adj.transactionID, adj.debitglAccountID, adj.creditglAccountID, adj.amount
	from dbo.tr_transactions as adj
	inner join dbo.tr_relationships as r on r.orgID = @orgID and r.typeID = @tr_AdjustTrans and r.transactionID = adj.transactionID
	inner join @tblHold as tbl on tbl.transactionID = r.appliedToTransactionID
	where adj.ownedByOrgID = @orgID 
	and adj.statusID = 1;

	-- wo
	INSERT INTO @tblHold
	select distinct wo.transactionID, wo.debitglAccountID, wo.creditglAccountID, wo.amount
	from dbo.tr_transactions as wo
	inner join dbo.tr_relationships as r on r.orgID = @orgID and r.typeID = @tr_WriteOffSaleTrans and r.transactionID = wo.transactionID
	inner join @tblHold as tbl on tbl.transactionID = r.appliedToTransactionID
	where wo.ownedByOrgID = @orgID 
	and wo.statusID = 1;

	-- dit
	INSERT INTO @tblHold
	select distinct dit.transactionID, dit.debitglAccountID, dit.creditglAccountID, dit.amount
	from dbo.tr_transactions as dit
	inner join dbo.tr_relationships as r on r.orgID = @orgID and r.typeID = @tr_DITSaleTrans and r.transactionID = dit.transactionID
	inner join @tblHold as tbl on tbl.transactionID = r.appliedToTransactionID
	where dit.ownedByOrgID = @orgID 
	and dit.statusID = 1;

	insert into @tblTrans
	select transactionID, debitglAccountID, amount, 0
	from @tblHold
		union all
	select transactionID, creditglAccountID, 0, amount
	from @tblHold;

	select gl.thePathExpanded + isnull(' (' + nullIf(gl.accountCode,'') + ')','') as glexpanded,
		case 
		when gl.accountType = 'Cash' and sd.debitAmount - sd.creditAmount >= 0 then sd.debitAmount - sd.creditAmount
		when gl.accountType = 'Asset' and gl.GLCode = 'ACCOUNTSRECEIVABLE' and sd.debitAmount - sd.creditAmount > 0 then sd.debitAmount - sd.creditAmount
		when gl.accountType = 'Liability' and gl.GLCode = 'DEPOSITS' and sd.creditAmount - sd.debitAmount <= 0 then abs(sd.creditAmount - sd.debitAmount)
		when gl.accountType = 'Revenue' and sd.creditAmount - sd.debitAmount < 0 then abs(sd.creditAmount - sd.debitAmount)
		when gl.accountType = 'Expense' and sd.debitAmount - sd.creditAmount >= 0 then sd.debitAmount - sd.creditAmount
		when gl.accountType = 'Liability' and sd.creditAmount - sd.debitAmount <= 0 then abs(sd.creditAmount - sd.debitAmount)
		else null
		end as debits,
		case 
		when gl.accountType = 'Cash' and sd.debitAmount - sd.creditAmount < 0 then abs(sd.debitAmount - sd.creditAmount)
		when gl.accountType = 'Asset' and gl.GLCode = 'ACCOUNTSRECEIVABLE' and sd.debitAmount - sd.creditAmount <= 0 then abs(sd.debitAmount - sd.creditAmount)
		when gl.accountType = 'Liability' and gl.GLCode = 'DEPOSITS' and sd.creditAmount - sd.debitAmount > 0 then sd.creditAmount - sd.debitAmount
		when gl.accountType = 'Revenue' and sd.creditAmount - sd.debitAmount >= 0 then sd.creditAmount - sd.debitAmount
		when gl.accountType = 'Expense' and sd.debitAmount - sd.creditAmount < 0 then abs(sd.debitAmount - sd.creditAmount)
		when gl.accountType = 'Liability' and sd.creditAmount - sd.debitAmount > 0 then sd.creditAmount - sd.debitAmount
		else null
		end as credits
	from (
		select glAccountID, sum(debitAmount) as debitAmount, sum(creditAmount) as creditAmount
		from @tblTrans
		group by glAccountID
		having (sum(debitAmount) > 0 OR sum(creditAmount) > 0) and sum(debitAmount) <> sum(creditAmount)
	) as sd
	inner join @allGLS as gl on gl.GLAccountID = sd.GLAccountID
	order by gl.thePath;
	
	-- deferred schedule
	select dit.recognitionDate, sum(t.amount) as recogAmt
	from dbo.tr_transactions as t
	inner join dbo.tr_relationships as r on r.orgID = @orgID and r.typeID = @tr_DITSaleTrans and r.transactionID = t.transactionID
	inner join (
		select @transactionID as transactionID
			union
		select adj.transactionID
		from dbo.tr_transactions as adj
		inner join dbo.tr_relationships as rInner on rInner.orgID = @orgID and rInner.typeID = @tr_AdjustTrans and rInner.transactionID = adj.transactionID and rInner.appliedToTransactionID = @transactionID
		where adj.ownedByOrgID = @orgID
		and adj.statusID = 1
	) as tbl on tbl.transactionID = r.appliedToTransactionID
	inner join dbo.tr_transactionDIT as dit on dit.orgID = @orgID and dit.transactionID = t.transactionID
	where t.ownedByOrgID = @orgID 
	and t.statusID = 1
	and dit.isActive = 1
	group by dit.recognitionDate
	order by dit.recognitionDate;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
