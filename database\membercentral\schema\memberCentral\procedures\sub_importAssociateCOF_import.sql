ALTER PROC dbo.sub_importAssociateCOF_import
@siteID int,
@subStatus varchar(30),
@recordedByMemberID int,
@importResult xml OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
		
	declare @subStatuses table (statusCode char(1));
	declare @orgID int;
	select @orgID = orgID from dbo.sites where siteID = @siteID;

	IF OBJECT_ID('tempdb..#tblSubCOFSubs') IS NOT NULL 
		DROP TABLE #tblSubCOFSubs;
	IF OBJECT_ID('tempdb..#tmpAuditLog') IS NOT NULL 
		DROP TABLE #tmpAuditLog;
	IF OBJECT_ID('tempdb..#tmpUpdatedInvoices') IS NOT NULL 
		DROP TABLE #tmpUpdatedInvoices;
	CREATE TABLE #tmpAuditLog (auditCode varchar(10), msg varchar(max));
	CREATE TABLE #tmpUpdatedInvoices (invoiceID int);

	BEGIN TRY
		insert into @subStatuses (statusCode)
		select li.listitem 
		from dbo.sub_statuses as st
		CROSS APPLY dbo.fn_VarcharListToTable(@substatus,',') as li
		where st.statusCode = li.listItem;

		select s.subscriberID, tmp.MCMemberPayProfileID, tmp.MCProfileID
		into #tblSubCOFSubs	
		from #mc_SubCOFImport as tmp
		inner join dbo.ams_members as m on m.orgID = @orgID and m.ActiveMemberID = tmp.MCMemberID
		inner join dbo.sub_subscribers as s on s.memberID = m.memberID and s.rootsubscriberID = s.subscriberID
		inner join dbo.sub_subscriptions as sub on sub.subscriptionID = s.subscriptionID
		inner join dbo.sub_types as t on t.typeID = sub.typeID and t.typeID = tmp.MCSubTypeID
		inner join dbo.sub_statuses st on st.statusID = s.statusID
		inner join @subStatuses as tmpStat on tmpStat.statusCode = st.statusCode;

		BEGIN TRAN;
			-- update sub_subscriber rows
			update NEWs
			set NEWs.payProfileID = tmp2.MCMemberPayProfileID,
				NEWs.MPProfileID = tmp2.MCProfileID
			from dbo.sub_subscribers as NEWs
			inner join #tblSubCOFSubs as tmp2 on tmp2.subscriberID = NEWs.subscriberID;

			INSERT INTO #tmpAuditLog (auditCode, msg)
			SELECT 'SUBS', 'Pay Profile ' + mpp.detail + ' associated to ' + 'Subscription [' + ss.subscriptionName + '] (SubscriberID: ' + CAST(s.subscriberID AS varchar(10)) + ')'
			FROM #tblSubCOFSubs as tmp
			INNER JOIN dbo.sub_subscribers as s on s.subscriberID = tmp.subscriberID
			INNER JOIN dbo.ams_memberPaymentProfiles as mpp on mpp.payProfileID = tmp.MCMemberPayProfileID
			INNER JOIN dbo.sub_subscriptions as ss on ss.subscriptionID = s.subscriptionID;

			-- update non-paid invoices
			update i
			set i.payProfileID = tmp2.MCMemberPayProfileID,
				i.MPProfileID = tmp2.MCProfileID
				OUTPUT INSERTED.invoiceID INTO #tmpUpdatedInvoices
			from dbo.tr_invoices as i
			inner join dbo.tr_invoiceTransactions as it on it.orgID = @orgID and it.invoiceID = i.invoiceID
			inner join dbo.tr_applications as tra on tra.orgID = @orgID and tra.transactionID = it.transactionID
				and tra.applicationTypeID = 17
				and tra.itemType = 'Dues'
			inner join dbo.sub_subscribers s on s.subscriberID = tra.itemID
			inner join #tblSubCOFSubs as tmp2 on tmp2.subscriberID = s.rootSubscriberID
			where i.orgID = @orgID
			and i.statusID <> 4
			and isnull(i.payProfileID,0) <> tmp2.MCMemberPayProfileID;

			-- inv
			INSERT INTO #tmpAuditLog (auditCode, msg)
			SELECT 'INV', 'Pay Profile ' + mpp.detail + ' associated to Invoice ' + o.orgCode + dbo.fn_tr_padInvoiceNumber(i.invoiceNumber)
			FROM dbo.tr_invoices as i
			INNER JOIN #tmpUpdatedInvoices as tmp on tmp.invoiceID = i.invoiceID
			INNER JOIN dbo.organizations as o on o.orgID = i.orgID
			INNER JOIN dbo.ams_memberPaymentProfiles as mpp on mpp.payProfileID = i.payProfileID;

			IF EXISTS (SELECT 1 FROM #tmpAuditLog) BEGIN
				INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
				SELECT '{ "c":"auditLog", "d": {
					"AUDITCODE":"' + auditCode + '",
					"ORGID":' + cast(@orgID as varchar(10)) + ',
					"SITEID":' + cast(@siteID as varchar(10)) + ',
					"ACTORMEMBERID":' + cast(@recordedByMemberID as varchar(20)) + ',
					"ACTIONDATE":"' + convert(varchar(20),GETDATE(),120) + '",
					"MESSAGE":"' + replace(memberCentral.dbo.fn_cleanInvalidXMLChars(msg),'"','\"') + '" } }'
				FROM #tmpAuditLog;
			END
		COMMIT TRAN;

	END TRY
	BEGIN CATCH
		IF @@trancount > 0 ROLLBACK TRANSACTION;

		INSERT INTO #tblSubCOFErrors (msg)
		VALUES ('Unable to associate cards on file with subscribers.');

		INSERT INTO #tblSubCOFErrors (msg)
		VALUES (left(error_message(),300));
	END CATCH

	select @importResult = (
		select getdate() as "@date",
			isnull((select top 100 PERCENT dbo.fn_RegExReplace(isnull(msg,''),'[^\x20-\x7E]','') as "@msg"
			from #tblSubCOFErrors
			order by rowid
			FOR XML path('error'), root('errors'), type),'<errors/>')
		for xml path('import'), TYPE);

	IF OBJECT_ID('tempdb..#tblSubCOFSubs') IS NOT NULL 
		DROP TABLE #tblSubCOFSubs;
	IF OBJECT_ID('tempdb..#tmpAuditLog') IS NOT NULL 
		DROP TABLE #tmpAuditLog;
	IF OBJECT_ID('tempdb..#tmpUpdatedInvoices') IS NOT NULL 
		DROP TABLE #tmpUpdatedInvoices;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
